import request from '/@/utils/request';

export function useAttendanceApi() {
	return {
		getAbsenceStatistics: (params?: any) => {
			return request({
				url: 'task/absence/statistics/',
				method: 'get',
				params,
			});
		},

		// 下载周报
		downloadWeeklyReport: (params: { start_date: string; end_date: string }) => {
			return request({
				url: '/task/management/weekly_report/download/',
				method: 'get',
				params,
				responseType: 'blob', // 指定响应类型为 blob
			});
		},
	};
}
