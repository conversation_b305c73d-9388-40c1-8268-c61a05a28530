import request from '/@/utils/request';

/**
 * （不建议写成 request.post(xxx)，因为这样 post 时，无法 params 与 data 同时传参）
 *
 * 任务列表api接口集合
 * @method getTaskInfoList 任务数据列表
 * @method addTaskWorkForTime 添加任务
 */
export function useDemandPoolApi() {
	return {
		getDemandPoolList: (params?: any) => {
			return request({
				url: '/task/needy/',
				method: 'get',
				params,
			});
		},
		addDemandPool: (data?: any) => {
			return request({
				url: '/task/needy/',
				method: 'post',
				data,
			});
		},
		updateDemandPool: (id?: any, data?: any) => {
			return request({
				url: `/task/needy/${id}/`,
				method: 'put',
				data,
			});
		},
		deleteDemandPool: (id?: any) => {
			return request({
				url: `/task/needy/${id}/`,
				method: 'delete',
			});
		},
		copyDemandPool: (id?: any, data?: any) => {
			return request({
				url: `/task/needy/copy/${id}/`,
				method: 'post',
				data,
			});
		},
		getTableHeader: (params?: any) => {
			return request({
				url: `/task/needy/get_needy_header/`,
				method: 'get',
				params,
			});
		},
		getTaskTableData: (params?: any) => {
			return request({
				url: `/task/get_user_needy/`,
				method: 'get',
				params,
			});
		},
		demandPoolWorkMoveIntoTable: (id?: any, data?: any) => {
			return request({
				url: `/task/needy/${id}/`,
				method: 'patch',
				data,
			});
		},
	};
}
