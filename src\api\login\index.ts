import request from '/@/utils/request';

/**
 * （不建议写成 request.post(xxx)，因为这样 post 时，无法 params 与 data 同时传参）
 *
 * 登录api接口集合
 * @method signIn 用户登录
 * @method register 用户注册
 */
export function useLoginApi() {
	return {
		register: (data: object) => {
			return request({
				url: '/user/create/',
				method: 'post',
				data,
			});
		},

		signIn: (data: object) => {
			return request({
				url: '/login',
				method: 'post',
				data,
			});
		},
		// signOut: (data: object) => {
		// 	return request({
		// 		url: '/user/signOut',
		// 		method: 'post',
		// 		data,
		// 	});
		// },
	};
}
