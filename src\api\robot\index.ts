import request from '/@/utils/request';

/**
 * （不建议写成 request.post(xxx)，因为这样 post 时，无法 params 与 data 同时传参）
 *
 * user用户相关接口集合
 * @method getWebhookInfo 获取机器人列表
 * @method addWebhook 添加机器人
 * @method editWebhookInfo 编辑机器人
 * @method delWebhookInfo 删除机器人
 * 
 */
export function useRobotApi() {
	return {
		getWebhookInfo: (params: object) => {
			return request({
				url: '/webhook/info/',
				method: 'get',
				params,
			});
		},
        addWebhook: (data: object) => {
			return request({
				url: '/webhook/info/',
				method: 'post',
				data,
			});
		},
        editWebhookInfo: (data: any) => {
			return request({
				url: `/webhook/info/${data.id}/`,
				method: 'put',
				data,
			});
		},
        delWebhookInfo: (id:any) => {
			return request({
				url: `/webhook/info/${id}/`,
				method: 'delete',
			});
		},
        getMessageList: (params: object) => {
			return request({
				url: '/timed/info/',
				method: 'get',
				params,
			});
		},
        addWxMessage: (data: object) => {
			return request({
				url: '/timed/info/',
				method: 'post',
				data,
			});
		},
        editWxMessage: (data: any) => {
			return request({
				url: `/timed/info/${data.id}/`,
				method: 'put',
				data,
			});
		},
        delWxMessage: (id:any) => {
			return request({
				url: `/timed/info/${id}/`,
				method: 'delete',
			});
		},
        editWxMessagePatch: (data: any) => {
			return request({
				url: `/timed/info/${data.id}/`,
				method: 'patch',
				data,
			});
		},

		
	};
}
