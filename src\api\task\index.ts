import request from '/@/utils/request';

/**
 * （不建议写成 request.post(xxx)，因为这样 post 时，无法 params 与 data 同时传参）
 *
 * 任务列表api接口集合
 * @method getTaskInfoList 任务数据列表
 * @method addTaskWorkForTime 添加任务
 */
export function useTaskApi() {
	return {
		getTaskInfoList: (params?: any) => {
			return request({
				url: '/task/info/',
				method: 'get',
				params,
			});
		},
		addTaskWorkForTime: (data?: any) => {
			return request({
				url: '/task/management/',
				method: 'post',
				data,
			});
		},
		addTaskWorkForTimeTwo: (data?: any) => {
			return request({
				url: '/task/work/',
				method: 'post',
				data,
			});
		},
		editTaskWorkForTime: (data: any, id: any) => {
			return request({
				url: `/task/work/${id}/`,
				method: 'put',
				data,
			});
		},
		delTaskForTime: (id: any) => {
			return request({
				url: `/task/work/${id}/`,
				method: 'delete',
			});
		},
		taskFeedback: (data: any, id: any) => {
			return request({
				url: `/task/work/feedback/${id}/`,
				method: 'put',
				data,
			});
		},
		copyPrevDay: (data: any) => {
			return request({
				url: `/task/management/copy/`,
				method: 'post',
				data,
			});
		},
		updateTaskProcess: (params: any) => {
			return request({
				url: '/api/task/process',
				method: 'post',
				data: params,
			});
		},
		// 更新任务进度
		updateTaskSchedule: (data: any, id: any) => {
			return request({
				url: `/task/work/schedule/update/${id}/`,
				method: 'put',
				data,
			});
		},
	};
}
