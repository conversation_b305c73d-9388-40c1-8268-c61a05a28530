import request from '/@/utils/request';

/**
 * （不建议写成 request.post(xxx)，因为这样 post 时，无法 params 与 data 同时传参）
 *
 * user用户相关接口集合
 * @method signIn 用户登录
 * @method signOut 用户退出登录
 */
export function useUserApi() {
	return {
		getUserList: (params: object) => {
			return request({
				url: '/user/userinfo/',
				method: 'get',
				params,
			});
		},
		getUserInfo: () => {
			return request({
				url: '/user/userinfo/self/',
				method: 'get',
			});
		},
		addUser: (data: object) => {
			return request({
				url: '/user/userinfo/',
				method: 'post',
				data,
			});
		},
		editUserInfo: (data: any, method: string = 'put') => {
			return request({
				url: `/user/userinfo/${data.id}/`,
				method,
				data,
			});
		},
		delUserInfo: (id: any, data: any) => {
			return request({
				url: `/user/userinfo/${id}/`,
				method: 'delete',
				data,
			});
		},
		reUseUser: (id: any, data: any) => {
			return request({
				url: `/user/userinfo/is_delete/${id}/`,
				method: 'post',
				data,
			});
		},
		getUserGroup: (params?: object) => {
			return request({
				url: '/user/operation/',
				method: 'get',
				params,
			});
		},
		addUserGroup: (data?: object) => {
			return request({
				url: '/user/operation/',
				method: 'post',
				data,
			});
		},
		updatePasswordForAdmin: (data: any, id: any) => {
			return request({
				url: `/user/userinfo/password/reset/${id}/`,
				method: 'put',
				data,
			});
		},
		updateOwnPassword: (data: any) => {
			return request({
				url: `/user/userinfo/password/update/`,
				method: 'put',
				data,
			});
		},
		updateCharacteristicGroup: (data: any, method: string) => {
			return request({
				url: `/user/characteristic_group/`,
				method,
				data,
			});
		},
		// 新的人员管理列表（按特性小组分组）
		getCharacteristicGroup: (params: object) => {
			return request({
				url: '/user/characteristic_group/',
				method: 'get',
				params,
			});
		},
		// 是否分配查看考勤权限
		editCheckWeeklyReportPermission: (id: any, data: object) => {
			return request({
				url: `/user/userinfo/check_weekly_report/${id}/`,
				method: 'post',
				data,
			});
		},
		// 获取特性小组列表（用于配置弹窗）
		getCharacteristicGroupList: (params?: object) => {
			return request({
				url: '/user/characteristic_group/',
				method: 'get',
				params,
			});
		},
		// 添加特性小组
		addCharacteristicGroup: (data: object) => {
			return request({
				url: '/user/characteristic_group/',
				method: 'post',
				data,
			});
		},
		// 编辑特性小组
		editCharacteristicGroup: (id: any, data: object) => {
			return request({
				url: `/user/characteristic_group/${id}/`,
				method: 'put',
				data,
			});
		},
		// 删除特性小组
		deleteCharacteristicGroup: (id: any) => {
			return request({
				url: `/user/characteristic_group/${id}/`,
				method: 'delete',
			});
		},
		// 设置特性小组管理员
		setCharacteristicGroupAdmin: (data: object) => {
			return request({
				url: '/user/characteristic_group/admin/',
				method: 'post',
				data,
			});
		},
	};
}
