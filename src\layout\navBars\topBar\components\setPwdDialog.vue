<template>
	<div class="system-user-dialog-container">
		<el-dialog v-model="state.isSetPwdDialog" title="修改登录密码" width="30%" draggable>
			<el-form ref="userPwdFormRef" :model="state.pwdForm" :rules="rules" label-width="80px">
				<el-form-item label="原密码" prop="PasswordOld">
					<el-input v-model="state.pwdForm.PasswordOld" type="password" placeholder="请输入原密码" show-password />
				</el-form-item>
				<el-form-item label="新密码" prop="PasswordNew">
					<el-input v-model="state.pwdForm.PasswordNew" type="password" placeholder="请输入新密码" show-password />
				</el-form-item>
				<el-form-item label="确认密码" prop="confirm_password">
					<el-input v-model="state.pwdForm.confirm_password" type="password" placeholder="请确认新密码"
						show-password />
				</el-form-item>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="closeDialog">取 消</el-button>
					<el-button type="primary" @click="confirmChangePwd(userPwdFormRef)">
						确 认
					</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts" name="setPwdDialogAll">
import { reactive, ref} from 'vue';
// 用户信息相关api
import { useUserApi } from '/@/api/user/index';
import { ElMessageBox, ElMessage, ClickOutside as vClickOutside, FormInstance, FormRules } from 'element-plus';
const userApi = useUserApi();
const userPwdFormRef = ref()
// 定义子组件向父组件传值/事件
// const emit = defineEmits(['refresh']);
const state = reactive({
	isScreenfull: false,
	disabledSize: 'large',
	isSetPwdDialog: false,
	pwdForm: {
		PasswordOld: '',
		PasswordNew: '',
		confirm_password: '',
	},
});

const rules = reactive<FormRules>({
	PasswordOld: [
		{ required: true, type: 'string' || 'number', message: "原密码不能为空", trigger: "blur" },
		// { min: 6, message: "密码长度不小于6个字符", trigger: "blur" },
	],
	PasswordNew: [
		{ required: true, type: 'string' || 'number', message: "新密码不能为空", trigger: "blur" },
		// { min: 6, message: "密码长度不小于6个字符", trigger: "blur" },
		{
			validator: (rule, value, callback) => {
				if (value.trim() === state.pwdForm.PasswordOld.trim()) {
					callback(
						new Error('新密码与原密码一样，请重新输入')
					);
				} else {
					callback();
				}
			},
			trigger: "blur",
		},
	],
	confirm_password: [
		{ required: true, type: 'string' || 'number', message: "确认密码不能为空", trigger: "blur" },
		// { min: 6, message: "密码长度不小于6个字符", trigger: "blur" },
		{
			validator: (rule, value, callback) => {
				if (value.trim() !== state.pwdForm.PasswordNew.trim()) {
					callback(
						new Error('两次输入密码不一样，请重新输入')
					);
				} else {
					callback();
				}
			},
			trigger: "blur",
		},
	],
});
// 重置表单
const resetForm = (formEl: FormInstance | undefined) => {
	if (!formEl) return;
	formEl.resetFields();
}
const closeDialog = () => {
	state.isSetPwdDialog = false
	state.pwdForm = {
		PasswordOld: '',
		PasswordNew: '',
		confirm_password: '',
	}
	userPwdFormRef.value.resetFields()
}
const confirmChangePwd = async (formEl: FormInstance | undefined) => {
	if (!formEl) return
	await formEl.validate(async (valid) => {
		if (valid) {
			const params = {
				old_password: state.pwdForm.PasswordOld,
				new_password: state.pwdForm.PasswordNew
			}
			await userApi.updateOwnPassword(params) 
			ElMessage.success('修改密码成功，退出登录生效');
			closeDialog()
		} else {
			ElMessage.error('请完善表单内容!');
		}
		// state.isSetPwdDialog = false;
	})
};
const openPwdDialog = () => {
	state.isSetPwdDialog = true
}
// 暴露变量
defineExpose({
	openPwdDialog
});
</script>
