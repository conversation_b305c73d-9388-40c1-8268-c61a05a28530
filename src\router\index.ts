import { createRouter, createWebHashHistory } from 'vue-router';
import NProgress from 'nprogress';
import 'nprogress/nprogress.css';
import pinia from '/@/stores/index';
import { storeToRefs } from 'pinia';
import { useKeepALiveNames } from '/@/stores/keepAliveNames';
import { useRoutesList } from '/@/stores/routesList';
import { useThemeConfig } from '/@/stores/themeConfig';
import { useUserInfo } from '/@/stores/userInfo';

import { staticRoutes, notFoundAndNoPower } from '/@/router/route';
import { initFrontEndControlRoutes } from '/@/router/frontEnd';
import { initBackEndControlRoutes } from '/@/router/backEnd';
import Cookies from 'js-cookie';
import { ElMessageBox } from 'element-plus';
/**
 * 1、前端控制路由时：isRequestRoutes 为 false，需要写 roles，需要走 setFilterRoute 方法。
 * 2、后端控制路由时：isRequestRoutes 为 true，不需要写 roles，不需要走 setFilterRoute 方法），
 * 相关方法已拆解到对应的 `backEnd.ts` 与 `frontEnd.ts`（他们互不影响，不需要同时改 2 个文件）。
 * 特别说明：
 * 1、前端控制：路由菜单由前端去写（无菜单管理界面，有角色管理界面），角色管理中有 roles 属性，需返回到 userInfo 中。
 * 2、后端控制：路由菜单由后端返回（有菜单管理界面、有角色管理界面）
 */

// 读取 `/src/stores/themeConfig.ts` 是否开启后端控制路由配置
const storesThemeConfig = useThemeConfig(pinia);
const { themeConfig } = storeToRefs(storesThemeConfig);
const { isRequestRoutes } = themeConfig.value;
const stores = useUserInfo();
const { userInfos } = storeToRefs(stores);

/**
 * 创建一个可以被 Vue 应用程序使用的路由实例
 * @method createRouter(options: RouterOptions): Router
 * @link 参考：https://next.router.vuejs.org/zh/api/#createrouter
 */
export const router = createRouter({
	history: createWebHashHistory(),
	/**
	 * 说明：
	 * 1、notFoundAndNoPower 默认添加 404、401 界面，防止一直提示 No match found for location with path 'xxx'
	 * 2、backEnd.ts(后端控制路由)、frontEnd.ts(前端控制路由) 中也需要加 notFoundAndNoPower 404、401 界面。
	 *    防止 404、401 不在 layout 布局中，不设置的话，404、401 界面将全屏显示
	 */
	routes: [...notFoundAndNoPower, ...staticRoutes],
});

/**
 * 路由多级嵌套数组处理成一维数组
 * @param arr 传入路由菜单数据数组
 * @returns 返回处理后的一维路由菜单数组
 */
export function formatFlatteningRoutes(arr: any) {
	if (arr.length <= 0) return false;
	for (let i = 0; i < arr.length; i++) {
		if (arr[i].children) {
			arr = arr.slice(0, i + 1).concat(arr[i].children, arr.slice(i + 1));
		}
	}
	return arr;
}

/**
 * 一维数组处理成多级嵌套数组（只保留二级：也就是二级以上全部处理成只有二级，keep-alive 支持二级缓存）
 * @description isKeepAlive 处理 `name` 值，进行缓存。顶级关闭，全部不缓存
 * @link 参考：https://v3.cn.vuejs.org/api/built-in-components.html#keep-alive
 * @param arr 处理后的一维路由菜单数组
 * @returns 返回将一维数组重新处理成 `定义动态路由（dynamicRoutes）` 的格式
 */
export function formatTwoStageRoutes(arr: any) {
	if (arr.length <= 0) return false;
	const newArr: any = [];
	const cacheList: Array<string> = [];

	// 找到根路由
	const rootRoute = arr.find((v: any) => v.path === '/');
	if (rootRoute) {
		newArr.push({
			component: rootRoute.component,
			name: rootRoute.name,
			path: rootRoute.path,
			redirect: rootRoute.redirect,
			meta: rootRoute.meta,
			children: [],
		});
	}

	arr.forEach((v: any) => {
		if (v.path === '/') return; // 跳过根路由

		// 检查是否应该作为独立路由
		const shouldBeIndependent = v.meta?.independent === true || v.path === '/exam/entry';

		if (shouldBeIndependent) {
			// 作为顶级路由
			if (v.path.indexOf('/:') > -1) {
				v.meta['isDynamic'] = true;
				v.meta['isDynamicPath'] = v.path;
			}
			newArr.push({ ...v });
		} else {
			// 作为子路由
			if (v.path.indexOf('/:') > -1) {
				v.meta['isDynamic'] = true;
				v.meta['isDynamicPath'] = v.path;
			}
			newArr[0].children.push({ ...v });

			// 处理缓存
			if (newArr[0].meta.isKeepAlive && v.meta.isKeepAlive) {
				cacheList.push(v.name);
				const stores = useKeepALiveNames(pinia);
				stores.setCacheKeepAlive(cacheList);
			}
		}
	});
	return newArr;
}

// 路由加载前
router.beforeEach(async (to, from, next) => {
	NProgress.configure({ showSpinner: false });
	if (to.meta.title) NProgress.start();
	let hasWxToken = Cookies.get('jwt');
	hasWxToken = '123';

	// 没有token的情况
	if (!hasWxToken) {
		ElMessageBox.confirm('jwt缺失,请刷新页面!', '提示', {
			confirmButtonText: '确认刷新',
			cancelButtonText: '取消',
			type: 'warning',
		})
			.then(() => {
				window.location.reload();
			})
			.catch(() => {
				// console.log('用户取消刷新');
			});
		return;
	}

	// 有token的情况
	if (to.path === '/login') {
		// 确保用户信息已加载
		if (userInfos.value && typeof userInfos.value.isAdmin !== 'undefined') {
			const defaultPath = userInfos.value.isAdmin ? '/arrangeTask' : '/home';
			next({ path: defaultPath });
		} else {
			next({ path: '/home' });
		}
		NProgress.done();
		return;
	}

	const storesRoutesList = useRoutesList(pinia);
	const { routesList } = storeToRefs(storesRoutesList);

	// 确保路由先初始化
	if (routesList.value.length === 0) {
		try {
			if (isRequestRoutes) {
				await initBackEndControlRoutes();
			} else {
				await initFrontEndControlRoutes();
			}

			// 等待用户信息加载完成后再进行路由判断
			if (userInfos.value === null || typeof userInfos.value.isTest === 'undefined') {
				next({ path: to.path, replace: true });
				return;
			}

			// 如果用户未完成测试且不在测试页面，重定向到测试页面
			if (!userInfos.value.isTest && to.path !== '/exam/entry') {
				next({ path: '/exam/entry' });
				return;
			}

			// 如果访问根路径，根据用户角色重定向到不同的默认页面
			if (to.path === '/') {
				const defaultPath = userInfos.value.isAdmin ? '/arrangeTask' : '/home';
				next({ path: defaultPath });
				return;
			}

			next({ path: to.path, query: to.query, replace: true });
			return;
		} catch (error) {
			next('/login');
			return;
		}
	}

	// 路由已初始化，检查用户测试状态
	if (!userInfos.value.isTest && to.path !== '/exam/entry') {
		next({ path: '/exam/entry' });
		return;
	}

	// 如果已完成测试且尝试访问测试页面，重定向到首页
	if (userInfos.value.isTest && to.path === '/exam/entry') {
		// 根据用户角色重定向到不同的默认页面
		const defaultPath = userInfos.value.isAdmin ? '/arrangeTask' : '/home';
		next({ path: defaultPath });
		return;
	}

	// 如果访问根路径，根据用户角色重定向到不同的默认页面
	if (to.path === '/') {
		const defaultPath = userInfos.value.isAdmin ? '/arrangeTask' : '/home';
		next({ path: defaultPath });
		return;
	}

	// 其他情况放行
	next();
});

// 路由加载后
router.afterEach(() => {
	NProgress.done();
});

// 导出路由
export default router;
