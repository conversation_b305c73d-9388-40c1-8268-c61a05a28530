import { defineStore } from 'pinia';
import Cookies from 'js-cookie';
import { Session } from '/@/utils/storage';
import { useUserApi } from '/@/api/user/index';
const userApi = useUserApi();
/**
 * 用户信息
 * @methods setUserInfos 设置用户信息
 */
export const useUserInfo = defineStore('userInfo', {
	state: (): UserInfosState => ({
		userInfos: {
			userName: '',
			photo: '',
			time: 0,
			roles: [],
			authBtnList: [],
		},
	}),
	actions: {
		async setUserInfos() {
			// 存储用户信息到浏览器缓存
			// if (Session.get('userInfo')) {
			// 	this.userInfos = Session.get('userInfo');
			// } else {
			// 	const userInfos = <UserInfos>await this.getApiUserInfo();
			// 	this.userInfos = userInfos;
			// }
			const userInfos = <UserInfos>await this.getApiUserInfo();
			this.userInfos = userInfos;
		},
		// 模拟接口数据
		// https://gitee.com/lyt-top/vue-next-admin/issues/I5F1HP
		async getApiUserInfo() {
			return new Promise((resolve) => {
				setTimeout(async () => {
					const res = await userApi.getUserInfo();
					const userName = res.data.username;
					const userId = res.data.id;
					const groupName = res.data.business_type;
					const groupId = res.data.group;
					const company = res.data.company;
					const isAdmin = res.data.is_admin;
					const isTest = res.data.is_test; // 是否完成了测验
					const isCheckWeeklyReport = res.data.check_weekly_report; // 是否有权限访问考勤统计（管理员或特性小组成员）
					const characteristicAdmin = !!res.data.characteristic_group_admin; // 是否是特性小组管理员
					// 模拟数据
					let defaultRoles: Array<string> = [];
					let defaultAuthBtnList: Array<string> = [];
					// admin 页面权限标识，对应路由 meta.roles，用于控制路由的显示/隐藏
					let adminRoles: Array<string> = ['admin'];
					// admin 按钮权限标识
					let adminAuthBtnList: Array<string> = ['btn.add', 'btn.del', 'btn.edit', 'btn.link'];
					// test 页面权限标识，对应路由 meta.roles，用于控制路由的显示/隐藏
					let testRoles: Array<string> = ['common'];
					// test 按钮权限标识
					let testAuthBtnList: Array<string> = ['btn.add', 'btn.link'];
					// 不同用户模拟不同的用户权限
					if (isAdmin || isCheckWeeklyReport) {
						// 管理员/有查看考勤的权限
						defaultRoles = adminRoles;
						defaultAuthBtnList = adminAuthBtnList;
					} else {
						defaultRoles = testRoles;
						defaultAuthBtnList = testAuthBtnList;
						if (characteristicAdmin) {
							defaultRoles.push('feature');
						}
					}
					// 用户信息模拟数据
					const userInfos = {
						userName: userName,
						userId,
						groupName,
						groupId,
						company,
						isAdmin,
						// photo:
						// 	userName === 'admin'
						// 		? 'https://img2.baidu.com/it/u=1978192862,2048448374&fm=253&fmt=auto&app=138&f=JPEG?w=504&h=500'
						// 		: 'https://img2.baidu.com/it/u=2370931438,70387529&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=500',

						time: new Date().getTime(),
						roles: defaultRoles,
						authBtnList: defaultAuthBtnList,
						isTest,
					};
					Session.set('userInfo', userInfos);
					resolve(userInfos);
				}, 0);
			});
		},
	},
});
