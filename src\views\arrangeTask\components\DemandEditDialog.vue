<template>
	<el-dialog 
		v-model="dialogVisible" 
		title="编辑需求" 
		width="500px"
		:before-close="handleClose"
	>
		<el-form :model="form" :rules="rules" ref="formRef" label-width="80px">
			<el-form-item label="需求类型" prop="type">
				<el-select v-model="form.type" placeholder="请选择需求类型">
					<el-option 
						v-for="category in demandCategories" 
						:key="category.value"
						:label="category.label" 
						:value="category.value"
					/>
				</el-select>
			</el-form-item>
			<el-form-item label="时间单位" prop="timeUnit">
				<el-select v-model="form.timeUnit" placeholder="请选择时间单位">
					<el-option label="上午(2h)" value="2" />
					<el-option label="下午(2h)" value="2-afternoon" />
					<el-option label="下午(4h)" value="4" />
					<el-option label="全天(6h)" value="6" />
					<el-option label="加班(4h)" value="4-overtime" />
				</el-select>
			</el-form-item>
			<el-form-item label="需求描述" prop="description">
				<el-input 
					v-model="form.description" 
					type="textarea" 
					:rows="3"
					placeholder="请输入需求描述"
				/>
			</el-form-item>
		</el-form>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="handleClose">取消</el-button>
				<el-button type="primary" @click="handleSubmit">确定</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';

// Props
interface Props {
	visible: boolean;
	demand?: any;
	personalDemands: any[];
}

const props = withDefaults(defineProps<Props>(), {
	visible: false,
	demand: null,
	personalDemands: () => []
});

// Emits
const emit = defineEmits(['update:visible', 'update:personalDemands']);

// 需求类别配置
const demandCategories = [
	{ label: '性能', value: 'perf' },
	{ label: '功能', value: 'func' },
	{ label: '协议', value: 'agree' },
	{ label: '开发', value: 'dev' },
	{ label: '公共', value: 'public' },
	{ label: '弱网', value: 'net' },
];

// 响应式数据
const formRef = ref();
const form = ref({
	id: 0,
	type: '',
	timeUnit: '',
	description: '',
	category: ''
});

const rules = {
	type: [
		{ required: true, message: '请选择需求类型', trigger: 'change' }
	],
	timeUnit: [
		{ required: true, message: '请选择时间单位', trigger: 'change' }
	],
	description: [
		{ required: true, message: '请输入需求描述', trigger: 'blur' }
	]
};

// 计算属性
const dialogVisible = computed({
	get: () => props.visible,
	set: (value) => emit('update:visible', value)
});

// 监听需求数据变化
watch(() => props.demand, (newDemand) => {
	if (newDemand) {
		form.value = {
			id: newDemand.id,
			type: newDemand.type,
			timeUnit: newDemand.timeUnit,
			description: newDemand.description,
			category: newDemand.category
		};
	}
}, { immediate: true });

// 方法
const handleClose = () => {
	dialogVisible.value = false;
	formRef.value?.resetFields();
};

const handleSubmit = async () => {
	try {
		await formRef.value?.validate();
		
		const updatedDemand = {
			...form.value,
			category: form.value.type // 更新category为新的type
		};
		
		// 在组件内部处理编辑逻辑
		const updatedDemands = props.personalDemands.map(demand => {
			if (demand.id === updatedDemand.id) {
				return {
					...demand,
					type: updatedDemand.type,
					timeUnit: updatedDemand.timeUnit,
					description: updatedDemand.description,
					category: updatedDemand.type
				};
			}
			return demand;
		});
		
		// TODO: 后期这里会调用编辑接口
		// await updateDemandApi(updatedDemand);
		
		emit('update:personalDemands', updatedDemands);
		handleClose();
	} catch (error) {
		// 表单验证失败或接口调用失败，不做处理
		// TODO: 后期可以添加错误提示
	}
};
</script>

<style scoped>
.dialog-footer {
	display: flex;
	justify-content: flex-end;
	gap: 12px;
}
</style> 