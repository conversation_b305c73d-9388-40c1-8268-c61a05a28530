<template>
	<div class="function-panel">
		<!-- 需求创建区域 -->
		<div class="panel-section create-section">
			<div class="section-title">
				{{ isEditMode ? '需求修改' : '需求创建' }}
				<!-- <div class="mode-toggle">
					<el-switch
						v-model="isEditMode"
						active-text="编辑"
						inactive-text="新建"
						@change="handleModeChange"
					/>
				</div> -->
			</div>
			<div class="section-content">
				<el-form :model="demandForm" label-position="top" :rules="demandRules" ref="demandFormRef">
					<el-row :gutter="5" style="margin-bottom: 10px;">
						<el-col :span="12">
							<el-form-item label="需求类别" prop="category">
								<el-select v-model="demandForm.category" size="default" style="width: 100%" placeholder="请选择">
									<el-option label="性能" value="perf" />
									<el-option label="功能" value="func" />
									<el-option label="协议" value="agree" />
									<el-option label="开发" value="dev" />
									<el-option label="公共" value="public" />
									<el-option label="弱网" value="net" />
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="12">
							<el-form-item label="反馈人" prop="leader">
								<el-select v-model="demandForm.leader" size="default" style="width: 100%" filterable placeholder="请选择反馈人">
									<el-option 
										v-for="user in feedbackUserList" 
										:key="user.id" 
										:label="user.real_name" 
										:value="user.id" 
									/>
								</el-select>
							</el-form-item>
						</el-col>
					</el-row>
					
					<el-form-item label="需求描述" prop="description">
						<el-input 
							type="textarea" 
							v-model="demandForm.description" 
							placeholder="请输入需求描述"
							:rows="1"
							:autosize="{ minRows: 1, maxRows: 5 }"
							maxlength="500"
							show-word-limit
						></el-input>
					</el-form-item>
					
					<el-form-item label="需求时长" prop="timeUnit">
						<el-select v-model="demandForm.timeUnit" size="default" style="width: 100%" placeholder="请选择需求时长">
							<el-option label="上午(全时段)/下午(2h)" value="2" />
							<el-option label="下午(全时段)/晚上(加班)" value="4" />
							<el-option label="全天" value="6" />
						</el-select>
					</el-form-item>
				</el-form>
				
				<div class="create-actions">
					<el-button 
						type="primary" 
						@click="isEditMode ? updateDemand() : createDemand()" 
						:disabled="!canCreateDemand"
					>
						{{ isEditMode ? '确认修改' : '创建需求' }}
					</el-button>
					<el-button 
						v-if="isEditMode" 
						@click="cancelEdit"
					>
						取消修改
					</el-button>
				</div>
			</div>
		</div>

		<!-- 任务详情展示区域 -->
		<div class="panel-section task-detail-section">
			<div class="section-title">任务详情</div>
			<div class="section-content">
				<div v-if="props.selectedTask" class="task-detail-content">
					<div class="detail-item">
						<span class="detail-label">工作类型：</span>
						<span class="detail-value">{{ getWorkTypeLabel(props.selectedTask.workType) }}</span>
					</div>
					<div class="detail-item">
						<span class="detail-label">任务类型：</span>
						<span class="detail-value">{{ getTaskTypeLabel(props.selectedTask.type) }}</span>
					</div>
					<div v-if="props.selectedTask.workType === 'original'" class="detail-item">
						<span class="detail-label">任务反馈人：</span>
						<span class="detail-value">{{ props.selectedTask.assignedByName || '未设置' }}</span>
					</div>
					<div class="detail-item">
						<span class="detail-label">工作执行人：</span>
						<span class="detail-value">{{ props.selectedTask.executorName || '未分配' }}</span>
					</div>
					<div class="detail-item">
						<span class="detail-label">工作日期：</span>
						<span class="detail-value">{{ formatWorkDate(props.selectedTask.workDate) }}</span>
					</div>
					<div class="detail-item">
						<span class="detail-label">工作时间：</span>
						<span class="detail-value">{{ getWorkTimeLabel(props.selectedTask.timeUnit, props.selectedTask.period) }}</span>
					</div>
					<div class="detail-item description-item">
						<span class="detail-label">任务描述：</span>
						<div class="detail-value description-content">{{ props.selectedTask.description || '暂无描述' }}</div>
					</div>
				</div>
				<div v-else class="empty-detail">
					<el-icon class="empty-icon"><Document /></el-icon>
					<p>请点击任务卡片查看详情</p>
				</div>
			</div>
		</div>

	</div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { Document } from '@element-plus/icons-vue';
import { useDemandPoolApi } from '/@/api/demandPool';
import { useUserApi } from '/@/api/user';
import { storeToRefs } from 'pinia';
import { useUserInfo } from '/@/stores/userInfo';
const { addDemandPool, updateDemandPool } = useDemandPoolApi();
const { getUserList } = useUserApi();
const stores = useUserInfo();
const { userInfos } = storeToRefs(stores);
// Props
interface Props {
	personalDemands: any[];
	selectedTask?: any;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits(['create-demand', 'update-demand']);

// 编辑模式状态
const isEditMode = ref(false);
const editingDemandId = ref<number | null>(null);

// 新建需求表单数据
const demandForm = ref({
	category: '',
	description: '',
	timeUnit: '6',
	leader: userInfos.value.userId || '',
});

// 表单验证规则
const demandRules = {
	category: [
		{ required: true, message: '请选择需求类别', trigger: 'blur' }
	],
	leader: [
		{ required: true, message: '请选择反馈人', trigger: 'blur' }
	],
	description: [
		{ required: true, message: '请输入需求描述', trigger: 'blur' },
		{ min: 3, message: '需求描述至少3个字符', trigger: 'blur' },
		{ max: 500, message: '需求描述不能超过500个字符', trigger: 'blur' }
	],
	timeUnit: [
		{ required: true, message: '请选择需求时长', trigger: 'blur' }
	]
};

// 表单引用
const demandFormRef = ref();

// 计算属性
const canCreateDemand = computed(() => {
	return demandForm.value.category && 
		demandForm.value.description.trim() && 
		demandForm.value.timeUnit &&
		demandForm.value.leader;
});

// 工具函数
const getDurationFromTimeUnit = (timeUnit: string): number => {
	switch (timeUnit) {
		case '2':
			return 2;
		case '4':
			return 4;
		case '6':
			return 6;
		default:
			return 0;
	}
};

// 方法定义
const resetForm = async (clearValidation = true) => {
	demandForm.value = {
		category: '',
		description: '',
		timeUnit: '',
		leader: userInfos.value.userId || '',
	};
	if (clearValidation) {
		// await nextTick();
		// demandFormRef.value?.clearValidate();
		nextTick(() => {
			demandFormRef.value?.clearValidate();
		});
	}
};

	const createDemand = async () => {
	try {
		await demandFormRef.value?.validate();
		const newDemand = {
			type: demandForm.value.category,
			timeUnit: demandForm.value.timeUnit,
			id: Date.now(),
			description: demandForm.value.description,
			category: demandForm.value.category,
			leader: demandForm.value.leader,
		};
		
		const params = {
				needy_type: demandForm.value.category,
				time_map: demandForm.value.timeUnit,
				description: demandForm.value.description,
				status: 1,
				is_feedback: false,
				duration: getDurationFromTimeUnit(demandForm.value.timeUnit),
				leader: demandForm.value.leader,
			};
		
		// 调用后端接口创建需求
		const result = await addDemandPool(params);
		
		if (result.code === 200) {
			emit('create-demand', newDemand);
			ElMessage.success('需求创建成功！');
			await resetForm();
		}
	} catch (error) {
		ElMessage.error('创建需求失败，请重试');
	}
};

const updateDemand = async () => {
	try {
		await demandFormRef.value?.validate();
		//接口参数
		const params = {
			id: editingDemandId.value, // 添加需求ID
			needy_type: demandForm.value.category,
			time_map: demandForm.value.timeUnit,
			description: demandForm.value.description,
			duration: getDurationFromTimeUnit(demandForm.value.timeUnit),
			leader: demandForm.value.leader,
		};
		
		// 调用后端接口更新需求
		const result = await updateDemandPool(editingDemandId.value, params);
		
		if (result.code === 200) {
			// 本地更新的数据
			const updatedDemand = {
				type: demandForm.value.category,
				timeUnit: demandForm.value.timeUnit,
				id: editingDemandId.value,
				description: demandForm.value.description,
				category: demandForm.value.category,
				leader: demandForm.value.leader,
			};
			
			emit('update-demand', updatedDemand);
			ElMessage.success('修改成功！');
			cancelEdit();
		}
	} catch (error) {
		ElMessage.error('修改失败，请重试');
	}
};

// const handleModeChange = () => {
// 	if (!isEditMode.value) {
// 		// 切换到新建模式时，清空表单
// 		cancelEdit();
// 	}
// };

const cancelEdit = async () => {
	isEditMode.value = false;
	editingDemandId.value = null;
	await resetForm();
};

// 暴露给父组件调用的编辑方法
const editDemand = (demand: any) => {
	isEditMode.value = true;
	editingDemandId.value = demand.id;
	demandForm.value = {
		category: demand.category,
		description: demand.description,
		timeUnit: demand.timeUnit,
		leader: demand.leader || userInfos.value.userId || '',
		...demand,
	};
};

// 任务详情格式化方法
const getWorkTypeLabel = (workType: string) => {
	const typeMap: Record<string, string> = {
		'original': '原定安排',
		'actual': '实际执行'
	};
	return typeMap[workType] || '原定安排';
};

const getTaskTypeLabel = (type: string) => {
	const typeMap: Record<string, string> = {
		'perf': '性能',
		'func': '功能', 
		'agree': '协议',
		'dev': '开发',
		'public': '公共',
		'net': '弱网'
	};
	return typeMap[type] || type;
};

const formatWorkDate = (workDate: string) => {
	if (!workDate) return '未设置';
	const date = new Date(workDate);
	return date.toLocaleDateString('zh-CN', {
		year: 'numeric',
		month: '2-digit',
		day: '2-digit'
	});
};

const getWorkTimeLabel = (timeUnit: string, period: string) => {
	// const timeMap: Record<string, string> = {
	// 	'2': '2h',
	// 	'4': '4h',
	// 	'6': '全天',
	// };
	
	const periodMap: Record<string, string> = {
		'morning': '10:00-12:00',
		'afternoon': '14:00-18:00',
		'afternoon_1': '14:00-16:00',
		'afternoon_2': '16:00-18:00',
		'afternoon_full': '14:00-18:00',
		'evening': '18:00-22:00',
		'fullday': '10:00-18:00'
	};
	
	// const timeLabel = timeMap[timeUnit] || '未知时长';
	const periodLabel = periodMap[period] || '';
	
	// return periodLabel ? `${periodLabel} - ${timeLabel}` : timeLabel;
	return periodLabel
};
//获取反馈人
const feedbackUserList:any = ref([]);
const getFeedbackUser = async () => {
	const result = await getUserList({
		page: 1,
		page_size: 1000,
		is_leader:1
	});
	if(result.code === 200){
		feedbackUserList.value = result.data;
	}else{
		ElMessage.error(result.msg);
	}
};
// 暴露方法给父组件
defineExpose({
	editDemand
});
onMounted(() => {
	getFeedbackUser();
});

</script>

<style scoped lang="scss">
.function-panel {
	display: flex;
	flex-direction: column;
	gap: 16px;
	overflow-y: auto;
	height: 100%;
	padding: 0 0px 0 0; // 右侧留出一点空间给分隔条

	.create-section {
		// background-color: #e8f3ff;
		
		.section-title {
			border-bottom-color: rgba(255, 255, 255, 0.3);
			
			.mode-toggle {
				display: flex;
				align-items: center;
				
				:deep(.el-switch) {
					.el-switch__label {
						font-size: 12px;
						font-weight: 500;
					}
					
					&.is-checked .el-switch__label--left {
						color: #909399;
					}
					
					&:not(.is-checked) .el-switch__label--right {
						color: #909399;
					}
				}
			}
		}
		
		:deep(.el-input__wrapper),
		:deep(.el-textarea__wrapper),
		:deep(.el-select) {
			background-color: rgba(255, 255, 255, 0.9);
		}
	}

	.task-detail-section {
		.task-detail-content {
			.detail-item {
				display: flex;
				align-items: flex-start;
				margin-bottom: 16px;
				
				&.description-item {
					flex-direction: column;
					align-items: stretch;
					
					.detail-label {
						margin-bottom: 8px;
					}
				}
				
				.detail-label {
					font-size: 14px;
					font-weight: 500;
					color: #606266;
					min-width: 80px;
					flex-shrink: 0;
				}
				
				.detail-value {
					font-size: 14px;
					color: #303133;
					flex: 1;
					
					&.description-content {
						background: #f5f7fa;
						padding: 12px;
						border-radius: 6px;
						border: 1px solid #e4e7ed;
						line-height: 1.5;
						min-height: 60px;
						word-break: break-word;
					}
				}
			}
		}
		
		.empty-detail {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			height: 200px;
			color: #909399;
			
			.empty-icon {
				font-size: 48px;
				margin-bottom: 16px;
				opacity: 0.5;
			}
			
			p {
				margin: 0;
				font-size: 14px;
			}
		}
	}
}

.panel-section {
	background: #fff;
	border-radius: 8px;
	padding: 20px;
	box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
	border: 1px solid #ebeef5;

	.section-title {
		font-size: 16px;
		font-weight: 600;
		padding-bottom: 12px;
		border-bottom: 2px solid #e4e7ed;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.section-content {
		.el-form-item {
			margin-bottom: 20px;
		}
	}
}

// 创建需求相关样式
.create-actions {
	display: flex;
	justify-content: center;
	gap: 12px;
	margin-top: 20px;
	padding-top: 16px;
	border-top: 1px solid rgba(255, 255, 255, 0.2);
	
	.el-button {
		border-radius: 6px;
		font-weight: 500;
		flex: 1;
		
		&.el-button--primary {
			background: #409eff;
			border-color: #409eff;
			color: white;
			
			&:hover {
				background: #66b1ff;
				border-color: #66b1ff;
				transform: translateY(-1px);
				box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
			}
		}
		
		&:not(.el-button--primary) {
			&:hover {
				transform: translateY(-1px);
			}
		}
	}
}
</style> 