<template>
	<div class="main-work-area">
		<!-- 拖拽删除区域 -->
		<div 
			class="delete-zone" 
			:class="deleteZoneClasses"
			@dragover.prevent
			@drop="handleDeleteDrop($event)"
			@dragenter="handleDeleteDragEnter($event)"
			@dragleave="handleDeleteDragLeave($event)"
		>
			<div class="delete-content">
				<el-icon class="delete-icon"><Delete /></el-icon>
				<span class="delete-text">拖拽到此处删除</span>
				<!-- <span class="delete-warning" v-else>松手删除该任务需求</span> -->
			</div>
		</div>
		
		<!-- 拖拽引导提示 -->
		<div 
			v-if="isDraggingForDelete && !deleteZoneVisible" 
			class="drag-guide-tip"
		>
			<div class="guide-content">
				<el-icon class="guide-icon"><ArrowUp /></el-icon>
				<span class="guide-text">拖拽到屏幕顶部可删除任务需求</span>
			</div>
		</div>
		<!-- 需求池区域 -->
		<div class="demand-pool-panel" :class="{ collapsed: !demandPoolExpanded }">
			<div class="panel-section">
				<div class="section-title">
					<div class="title-left">
						个人需求池({{ personalDemands.length }})
						<div class="expand-icon" @click="toggleDemandPool">
							<el-icon>
								<ArrowUp v-if="demandPoolExpanded" title="点击收起" />
								<ArrowDown v-else title="点击展开" />
							</el-icon>
						</div>
					</div>
					<div class="pool-controls">
						<div class="control-item">
							<el-switch v-model="copyMode" active-text="复用" inactive-text="" size="small" @change="handleCopyModeChange" />
						</div>
						<div class="control-item">
							<el-switch v-model="deleteMode" active-text="删除" inactive-text="" size="small" @change="handleDeleteModeChange" />
						</div>
					</div>
				</div>
				<div
					v-show="demandPoolExpanded"
					class="section-content demand-pool-drop-zone"
					:class="getDragHighlightClasses(null, 'demand-pool', '')"
					@dragover.prevent
					@drop="handleDemandPoolDrop($event)"
					@dragenter="handleDragEnter(null, 'demand-pool', '')"
					@dragleave="handleDragLeave"
				>
					<div v-if="personalDemands.length > 0" class="demand-pool-container">
						<!-- 固定的类别标题行 -->
						<div class="category-headers-row">
							<div v-for="category in demandCategories" :key="category.value" class="category-header-cell">
								{{ category.label }}
							</div>
						</div>

						<!-- 可滚动的需求内容区域 -->
						<div class="demand-content-area">
							<!-- 类别高亮层 -->
							<div 
								v-if="categoryDragHighlight" 
								class="category-highlight-overlay"
								:style="getCategoryHighlightStyle(categoryDragHighlight)"
							>
								<div class="drag-hint-text">
									拖拽到此处改变需求类型
								</div>
							</div>
							<div class="demand-categories-grid">
								<div 
									v-for="category in demandCategories" 
									:key="category.value" 
									class="category-column"
									:class="getCategoryDropZoneClass()"
									@dragover.prevent
									@drop="handleCategoryDrop($event, category.value)"
									@dragenter="handleCategoryDragEnter($event, category.value)"
									@dragleave="handleCategoryDragLeave($event)"
								>
									<div class="demand-items-container">
										<div
											v-for="demand in getDemandsByCategory(category.value)"
											:key="demand.id"
											class="demand-card"
											draggable="true"
											@dragstart="handleDemandDragStart($event, demand)"
											:style="{ backgroundColor: getTaskBgColor(demand.type) }"
											@click="handleDemandCardClick(demand, $event)"
											@dragend="handleDemandDragEnd"
										>
											<div class="demand-content">
												<div class="demand-time-badge" :class="{ 'multi-line': formatDemandTimeUnit(demand.timeUnit).isMultiLine }">
													<template v-if="formatDemandTimeUnit(demand.timeUnit).isMultiLine">
														<div class="time-line">{{ formatDemandTimeUnit(demand.timeUnit).firstLine }}</div>
														<div class="time-divider"></div>
														<div class="time-line">{{ formatDemandTimeUnit(demand.timeUnit).secondLine }}</div>
													</template>
													<template v-else>
														{{ formatDemandTimeUnit(demand.timeUnit).singleLine }}
													</template>
												</div>
												<div class="demand-description">{{ demand.description }}</div>
											</div>

											<!-- 复制按钮 -->
											<div v-if="copyMode" class="action-button copy-button" @click.stop="handleCopyDemand(demand)">+1</div>

											<!-- 删除按钮 -->
											<div v-if="deleteMode" class="action-button delete-button" @click.stop="handleDeleteDemand(demand.id)">×</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div v-else class="empty-pool">
						<el-icon class="empty-icon"><Box /></el-icon>
						<p>暂无需求，请先创建需求</p>
						<p class="empty-tip">您也可以将已分配的任务拖拽到此处</p>
					</div>
				</div>
			</div>
		</div>

		<!-- 人员安排区域 -->
		<div class="arrangement-panel">
			<div class="panel-section">
				<div class="section-title">
					任务安排表
					<span v-if="props.weekDateRange.startDate && props.weekDateRange.endDate" class="week-range">
						({{ props.weekDateRange.startDate }}-{{ props.weekDateRange.endDate }})
					</span>
					<span v-if="taskCopyMode" class="copy-mode-tip">
						<el-icon><DocumentCopy /></el-icon>
						当前为复制模式
					</span>
					<div class="arrangement-controls">
						<div class="week-nav">
							<div class="week-nav-button prev-week" @click="changeWeek(-1)">
								<span class="week-nav-text">上周</span>
							</div>
							<div class="week-nav-button current-week" @click="changeWeek(0)">
								<span class="week-nav-text">重置</span>
							</div>
							<div class="week-nav-button next-week" @click="changeWeek(1)">
								<span class="week-nav-text">下周</span>
							</div>
						</div>
					</div>
				</div>
				<div class="section-content">
					<!-- 显示模式控制区域 -->
					<div class="display-mode-controls">
						<div class="display-mode-controls-left">
							<el-radio-group v-model="displayMode" size="small">
								<el-radio-button label="original">原定</el-radio-button>
								<el-radio-button label="actual">实际</el-radio-button>
								<el-radio-button label="both">原定&实际</el-radio-button>
							</el-radio-group>
							<div class="filter-controls">
								<div class="filter-item">
									<label class="filter-label">工作时段</label>
									<el-select v-model="localFilterForm.workType" size="small" @change="workTypeChange" placeholder="全部" style="width: 120px">
										<el-option label="全部" value="all" />
										<el-option label="常规工作时段" value="normal" />
										<el-option label="加班工作时段" value="overtime" />
									</el-select>
								</div>
								<div class="filter-item">
									<label class="filter-label">业务</label>
									<el-select
										v-model="localFilterForm.business"
										size="small"
										@change="businessChange"
										placeholder="请选择业务"
										clearable
										style="width: 120px"
									>
										<el-option
											v-for="business in localFilterForm.businessOptions"
											:key="business.value"
											:label="business.label"
											:value="business.value"
										/>
									</el-select>
								</div>

								<div class="filter-item">
									<label class="filter-label">人员</label>
									<el-cascader
										v-model="localFilterForm.person"
										:options="cascaderOptions"
										:props="cascaderProps"
										placeholder="请选择人员"
										clearable
										collapse-tags
										:max-collapse-tags="4"
										@change="personChange"
										size="small"
										style="width: 260px"
									/>
								</div>
							</div>
						</div>
						<!-- <div class="task-copy-switch">
							<el-switch v-model="taskCopyMode" active-text="任务复用" inactive-text="" size="small" />
						</div> -->
						<div class="self-only-switch">
							<el-switch v-model="taskCopyMode" style="margin-right: 15px;" active-text="任务复用" inactive-text="" size="small" />
							<el-switch v-model="onlyShowSelf"  active-text="只看自己" inactive-text="" size="small" />
						</div>
					</div>

					<div class="task-table">
						<el-table
							:data="filteredTableData"
							border
							:row-key="getRowKey"
							:header-cell-style="{ 'text-align': 'center', 'background-color': '#fafafa' }"
							:cell-style="{ 'text-align': 'center' }"
							height="100%"
							:header-fixed="true"
						>
							<el-table-column prop="person" label="人员" width="100" fixed="left" />
							<el-table-column prop="businessGroup" label="业务" width="110" fixed="left" />
							<el-table-column
								v-for="dayData in weekDatesData"
								:key="dayData.key"
								:min-width="displayMode === 'both' ? 320 : 140"
								:width="displayMode === 'both' ? 320 : undefined"
								:class-name="isToday(dayData) ? 'today-column' : ''"
							>
								<template #header>
									<div class="day-header" :class="{ 'today-header': isToday(dayData) }">{{ getDayLabel(dayData.key) }}({{ dayData.dateStr }})</div>
								</template>
								<template #default="scope">
									<div class="task-cell" :style="{ height: taskCellHeight }">
										<!-- 原定模式：只显示原定安排 -->
										<template v-if="displayMode === 'original'">
											<!-- 检查是否有全天任务，如果有则合并显示（只要同时显示上午和下午就合并） -->
											<template
												v-if="getFullDayTask(scope.row, dayData.key) && visiblePeriods.includes('morning') && visiblePeriods.includes('afternoon')"
											>
												<!-- 全天任务合并展示区域（上午+下午） -->
												<div
													class="full-day-merged-container"
													:class="getDragHighlightClasses(scope.row, dayData.key, 'fullday')"
													@dragover.prevent
													@drop="handleDrop($event, scope.row, dayData.key, 'fullday')"
													@dragenter="handleDragEnter(scope.row, dayData.key, 'fullday')"
													@dragleave="handleDragLeave"
												>
													<div
														:class="getTaskCardClass(getFullDayTask(scope.row, dayData.key))"
														class="full-day-merged-task"
														:style="{
															backgroundColor: getTaskCardBgColor(getFullDayTask(scope.row, dayData.key)),
															minHeight: '100%',
														}"
														:draggable="isTaskDraggable(getFullDayTask(scope.row, dayData.key))"
														@dragstart="handleTaskDragStart($event, getFullDayTask(scope.row, dayData.key), scope.row, dayData.key, 'fullday')"
														@dragend="handleDragEnd"
														@contextmenu.prevent="handleTaskContextMenu($event, scope.row, dayData.key, 'fullday')"
														@click.stop="handleTaskClick(getFullDayTask(scope.row, dayData.key), scope.row, dayData.key, 'fullday', 'original')"
													>
														<div class="demand-content">
															<div class="demand-time-badge">{{ formatTimeUnit(getFullDayTask(scope.row, dayData.key).timeUnit, 'fullday') }}</div>
															<div class="demand-description">{{ getTaskDisplayDescription(getFullDayTask(scope.row, dayData.key)) }}</div>
														</div>
													</div>
												</div>

												<!-- 如果还有晚上时间段，单独处理晚上 -->
												<template v-if="visiblePeriods.includes('evening')">
													<div
														class="time-period evening-period"
														:class="getDragHighlightClasses(scope.row, dayData.key, 'evening')"
														@dragover.prevent
														@drop="handleDrop($event, scope.row, dayData.key, 'evening')"
														@dragenter="handleDragEnter(scope.row, dayData.key, 'evening')"
														@dragleave="handleDragLeave"
														@dragend="handleDragEnd"
													>
														<!-- 时间段标签 - 只在没有内容时显示 -->
														<div v-if="!hasTaskInPeriod(scope.row, dayData.key, 'evening')" class="period-label">{{ getPeriodLabel('evening') }}</div>

														<!-- 任务展示区域 -->
														<div class="tasks-container">
															<template v-if="getEveningTask(scope.row, dayData.key)">
																<div
																	:class="getTaskCardClass(getEveningTask(scope.row, dayData.key))"
																	:style="{
																		backgroundColor: getTaskCardBgColor(getEveningTask(scope.row, dayData.key)),
																		marginBottom: '-10px',
																	}"
																	:draggable="isTaskDraggable(getEveningTask(scope.row, dayData.key))"
																	@dragstart="handleTaskDragStart($event, getEveningTask(scope.row, dayData.key), scope.row, dayData.key, 'evening')"
																	@dragend="handleDragEnd"
																	@contextmenu.prevent="handleTaskContextMenu($event, scope.row, dayData.key, 'evening')"
																	@click.stop="handleTaskClick(getEveningTask(scope.row, dayData.key), scope.row, dayData.key, 'evening', 'original')"
																>
																	<div class="demand-content">
																		<div class="demand-time-badge">
																			{{ formatTimeUnit(getEveningTask(scope.row, dayData.key).timeUnit, 'evening') }}
																		</div>
																		<div class="demand-description">{{ getEveningTask(scope.row, dayData.key).description }}</div>
																	</div>
																</div>
															</template>
														</div>
													</div>
												</template>
											</template>

											<!-- 常规时间段展示 -->
											<template v-else>
												<div
													class="time-period"
													v-for="(period, index) in visiblePeriods"
													:key="index"
													@dragover.prevent
													@drop="handleDrop($event, scope.row, dayData.key, period)"
													@dragenter="handleDragEnter(scope.row, dayData.key, period)"
													@dragleave="handleDragLeave"
													:class="[getPeriodClass(period), getDragHighlightClasses(scope.row, dayData.key, period)]"
												>
													<!-- 时间段标签 - 只在没有内容时显示 -->
													<div v-if="!hasTaskInPeriod(scope.row, dayData.key, period)" class="period-label">{{ getPeriodLabel(period) }}</div>

													<!-- 任务展示区域 -->
													<div class="tasks-container">
														<!-- 处理上午时间段 -->
														<template v-if="period === 'morning'">
															<template v-if="getFullDayTask(scope.row, dayData.key)">
																<div
																	:class="getTaskCardClass(getFullDayTask(scope.row, dayData.key))"
																	:style="{
																		backgroundColor: getTaskCardBgColor(getFullDayTask(scope.row, dayData.key)),
																		marginBottom: '-10px',
																	}"
																	:draggable="isTaskDraggable(getFullDayTask(scope.row, dayData.key))"
																	@dragstart="handleTaskDragStart($event, getFullDayTask(scope.row, dayData.key), scope.row, dayData.key, 'fullday')"
																	@dragend="handleDragEnd"
																	@contextmenu.prevent="handleTaskContextMenu($event, scope.row, dayData.key, 'fullday')"
																	@click.stop="handleTaskClick(getFullDayTask(scope.row, dayData.key), scope.row, dayData.key, 'fullday', 'original')"
																>
																	<div class="demand-content">
																		<div class="demand-time-badge">
																			{{ formatTimeUnit(getFullDayTask(scope.row, dayData.key).timeUnit, 'fullday') }}
																		</div>
																		<div class="demand-description">{{ getFullDayTask(scope.row, dayData.key).description }}</div>
																	</div>
																</div>
															</template>
															<template v-else-if="getMorningTask(scope.row, dayData.key)">
																<div
																	:class="getTaskCardClass(getMorningTask(scope.row, dayData.key))"
																	:style="{
																		backgroundColor: getTaskCardBgColor(getMorningTask(scope.row, dayData.key)),
																		marginBottom: '-10px',
																	}"
																	:draggable="isTaskDraggable(getMorningTask(scope.row, dayData.key))"
																	@dragstart="handleTaskDragStart($event, getMorningTask(scope.row, dayData.key), scope.row, dayData.key, 'morning')"
																	@dragend="handleDragEnd"
																	@contextmenu.prevent="handleTaskContextMenu($event, scope.row, dayData.key, 'morning')"
																	@click.stop="handleTaskClick(getMorningTask(scope.row, dayData.key), scope.row, dayData.key, 'morning', 'original')"
																>
																	<div class="demand-content">
																		<div class="demand-time-badge">
																			{{ formatTimeUnit(getMorningTask(scope.row, dayData.key).timeUnit, 'morning') }}
																		</div>
																		<div class="demand-description">{{ getMorningTask(scope.row, dayData.key).description }}</div>
																	</div>
																</div>
															</template>
														</template>

														<!-- 处理下午时间段 - 恢复分层展示 -->
														<template v-else-if="period === 'afternoon'">
															<!-- 全天任务的下午部分 -->
															<template v-if="getFullDayTask(scope.row, dayData.key)">
																<div
																	:class="getTaskCardClass(getFullDayTask(scope.row, dayData.key))"
																	:style="{
																		backgroundColor: getTaskCardBgColor(getFullDayTask(scope.row, dayData.key)),
																	}"
																	@click.stop="handleTaskClick(getFullDayTask(scope.row, dayData.key), scope.row, dayData.key, 'fullday', 'original')"
																>
																	<div class="demand-content">
																		<div class="demand-time-badge">
																			{{ formatTimeUnit(getFullDayTask(scope.row, dayData.key).timeUnit, 'fullday') }}
																		</div>
																		<div class="demand-description">{{ getFullDayTask(scope.row, dayData.key).description }}</div>
																	</div>
																</div>
															</template>
															<!-- 下午任务区域 - 维持分层展示 -->
															<template v-else>
																<div class="afternoon-tasks-area">
																	<!-- 下午4小时任务 -->
																	<template v-if="getAfternoonFullTask(scope.row, dayData.key)">
																		<div
																			:class="getTaskCardClass(getAfternoonFullTask(scope.row, dayData.key))"
																			:style="{
																				backgroundColor: getTaskCardBgColor(getAfternoonFullTask(scope.row, dayData.key)),
																				minHeight: '100%',
																			}"
																			:draggable="isTaskDraggable(getAfternoonFullTask(scope.row, dayData.key))"
																			@dragstart="
																				handleTaskDragStart(
																					$event,
																					getAfternoonFullTask(scope.row, dayData.key),
																					scope.row,
																					dayData.key,
																					'afternoon_full'
																				)
																			"
																			@dragend="handleDragEnd"
																			@contextmenu.prevent="handleTaskContextMenu($event, scope.row, dayData.key, 'afternoon_full')"
																			@click.stop="
																				handleTaskClick(
																					getAfternoonFullTask(scope.row, dayData.key),
																					scope.row,
																					dayData.key,
																					'afternoon_full',
																					'original'
																				)
																			"
																		>
																			<div class="demand-content">
																				<div class="demand-time-badge">
																					{{ formatTimeUnit(getAfternoonFullTask(scope.row, dayData.key).timeUnit, 'afternoon_full') }}
																				</div>
																				<div class="demand-description">{{ getAfternoonFullTask(scope.row, dayData.key).description }}</div>
																			</div>
																		</div>
																	</template>
																	<!-- 下午2小时任务分区 - 恢复默认分层 -->
																	<template v-else>
																		<div class="afternoon-split-area">
																			<div
																				class="afternoon-slot slot-14-16"
																				:class="{
																					'has-task': getAfternoonTask1(scope.row, dayData.key),
																					...getDragHighlightClasses(scope.row, dayData.key, 'afternoon_1'),
																				}"
																				@dragover.prevent
																				@drop="handleAfternoonSlotDrop($event, scope.row, dayData.key, 'afternoon_1')"
																				@dragenter="handleDragEnter(scope.row, dayData.key, 'afternoon_1')"
																				@dragleave="handleDragLeave"
																			>
																				<!-- 时间标签只在没有任务时显示 -->
																				<div v-if="!getAfternoonTask1(scope.row, dayData.key)" class="time-slot-label">14:00-16:00</div>
																				<template v-if="getAfternoonTask1(scope.row, dayData.key)">
																					<div
																						:class="getTaskCardClass(getAfternoonTask1(scope.row, dayData.key)) + ' small-task'"
																						:style="{
																							backgroundColor: getTaskCardBgColor(getAfternoonTask1(scope.row, dayData.key)),
																						}"
																						:draggable="isTaskDraggable(getAfternoonTask1(scope.row, dayData.key))"
																						@dragstart="
																							handleTaskDragStart(
																								$event,
																								getAfternoonTask1(scope.row, dayData.key),
																								scope.row,
																								dayData.key,
																								'afternoon_1'
																							)
																						"
																						@dragend="handleDragEnd"
																						@contextmenu.prevent="handleTaskContextMenu($event, scope.row, dayData.key, 'afternoon_1')"
																						@click.stop="
																							handleTaskClick(
																								getAfternoonTask1(scope.row, dayData.key),
																								scope.row,
																								dayData.key,
																								'afternoon_1',
																								'original'
																							)
																						"
																					>
																						<div class="demand-content small">
																							<div class="demand-time-badge small">{{ formatTimeUnit('2', 'afternoon_1') }}</div>
																							<div class="demand-description small">{{ getAfternoonTask1(scope.row, dayData.key).description }}</div>
																						</div>
																					</div>
																				</template>
																			</div>
																			<div
																				class="afternoon-slot slot-16-18"
																				:class="{
																					'has-task': getAfternoonTask2(scope.row, dayData.key),
																					...getDragHighlightClasses(scope.row, dayData.key, 'afternoon_2'),
																				}"
																				@dragover.prevent
																				@drop="handleAfternoonSlotDrop($event, scope.row, dayData.key, 'afternoon_2')"
																				@dragenter="handleDragEnter(scope.row, dayData.key, 'afternoon_2')"
																				@dragleave="handleDragLeave"
																			>
																				<!-- 时间标签只在没有任务时显示 -->
																				<div v-if="!getAfternoonTask2(scope.row, dayData.key)" class="time-slot-label">16:00-18:00</div>
																				<template v-if="getAfternoonTask2(scope.row, dayData.key)">
																					<div
																						:class="getTaskCardClass(getAfternoonTask2(scope.row, dayData.key)) + ' small-task'"
																						:style="{
																							backgroundColor: getTaskCardBgColor(getAfternoonTask2(scope.row, dayData.key)),
																						}"
																						:draggable="isTaskDraggable(getAfternoonTask2(scope.row, dayData.key))"
																						@dragstart="
																							handleTaskDragStart(
																								$event,
																								getAfternoonTask2(scope.row, dayData.key),
																								scope.row,
																								dayData.key,
																								'afternoon_2'
																							)
																						"
																						@dragend="handleDragEnd"
																						@contextmenu.prevent="handleTaskContextMenu($event, scope.row, dayData.key, 'afternoon_2')"
																						@click.stop="
																							handleTaskClick(
																								getAfternoonTask2(scope.row, dayData.key),
																								scope.row,
																								dayData.key,
																								'afternoon_2',
																								'original'
																							)
																						"
																					>
																						<div class="demand-content small">
																							<div class="demand-time-badge small">{{ formatTimeUnit('2', 'afternoon_2') }}</div>
																							<div class="demand-description small">{{ getAfternoonTask2(scope.row, dayData.key).description }}</div>
																						</div>
																					</div>
																				</template>
																			</div>
																		</div>
																	</template>
																</div>
															</template>
														</template>

														<!-- 处理晚上时间段 -->
														<template v-else-if="period === 'evening'">
															<template v-if="getEveningTask(scope.row, dayData.key)">
																<div
																	:class="getTaskCardClass(getEveningTask(scope.row, dayData.key))"
																	:style="{
																		backgroundColor: getTaskCardBgColor(getEveningTask(scope.row, dayData.key)),
																		marginBottom: '-10px',
																	}"
																	:draggable="isTaskDraggable(getEveningTask(scope.row, dayData.key))"
																	@dragstart="handleTaskDragStart($event, getEveningTask(scope.row, dayData.key), scope.row, dayData.key, 'evening')"
																	@dragend="handleDragEnd"
																	@contextmenu.prevent="handleTaskContextMenu($event, scope.row, dayData.key, 'evening')"
																	@click.stop="handleTaskClick(getEveningTask(scope.row, dayData.key), scope.row, dayData.key, 'evening', 'original')"
																>
																	<div class="demand-content">
																		<div class="demand-time-badge">
																			{{ formatTimeUnit(getEveningTask(scope.row, dayData.key).timeUnit, 'evening') }}
																		</div>
																		<div class="demand-description">{{ getEveningTask(scope.row, dayData.key).description }}</div>
																	</div>
																</div>
															</template>
														</template>
													</div>
												</div>
											</template>
										</template>

										<!-- 实际模式：只显示实际执行内容 -->
										<template v-else-if="displayMode === 'actual'">
											<!-- 检查是否有全天实际任务，如果有则合并显示 -->
											<template
												v-if="
													getActualFullDayTask(scope.row, dayData.key) && visiblePeriods.includes('morning') && visiblePeriods.includes('afternoon')
												"
											>
												<!-- 全天实际任务合并展示区域 -->
												<div class="full-day-merged-container">
													<div
														class="task-item full-day-merged-task actual-task"
														:style="{
															backgroundColor: getActualTaskCardBgColor(getActualFullDayTask(scope.row, dayData.key)),
															minHeight: '100%',
														}"
														@click.stop="handleTaskClick(getActualFullDayTask(scope.row, dayData.key), scope.row, dayData.key, 'fullday', 'actual')"
													>
														<div class="demand-content">
															<div class="demand-time-badge">
																{{ formatTimeUnit(getActualFullDayTask(scope.row, dayData.key).timeUnit, 'fullday') }}
															</div>
															<div class="demand-description">{{ getActualFullDayTask(scope.row, dayData.key).description }}</div>
														</div>
													</div>
												</div>

												<!-- 如果还有晚上时间段，单独处理晚上 -->
												<template v-if="visiblePeriods.includes('evening')">
													<div
														class="time-period evening-period"
														:class="{ 'drag-highlight': isDragHighlight(scope.row, dayData.key, 'evening') }"
														@dragenter="handleDragEnter(scope.row, dayData.key, 'evening')"
														@dragleave="handleDragLeave"
													>
														<div v-if="!getActualEveningTask(scope.row, dayData.key)" class="period-label">{{ getPeriodLabel('evening') }}</div>
														<div class="tasks-container">
															<template v-if="getActualEveningTask(scope.row, dayData.key)">
																<div
																	class="task-item actual-task"
																	:style="{
																		backgroundColor: getActualTaskCardBgColor(getActualEveningTask(scope.row, dayData.key)),
																		marginBottom: '-10px',
																	}"
																	@click.stop="
																		handleTaskClick(getActualEveningTask(scope.row, dayData.key), scope.row, dayData.key, 'evening', 'actual')
																	"
																>
																	<div class="demand-content">
																		<div class="demand-time-badge">
																			{{ formatTimeUnit(getActualEveningTask(scope.row, dayData.key).timeUnit, 'evening') }}
																		</div>
																		<div class="demand-description">{{ getActualEveningTask(scope.row, dayData.key).description }}</div>
																	</div>
																</div>
															</template>
														</div>
													</div>
												</template>
											</template>

											<!-- 常规实际任务时间段展示 -->
											<template v-else>
												<div
													class="time-period"
													v-for="(period, index) in visiblePeriods"
													:key="index"
													:class="[getPeriodClass(period), { 'drag-highlight': isDragHighlight(scope.row, dayData.key, `${period}_actual`) }]"
													@dragenter="handleDragEnter(scope.row, dayData.key, `${period}_actual`)"
													@dragleave="handleDragLeave"
												>
													<div v-if="!hasActualTaskInPeriod(scope.row, dayData.key, period)" class="period-label">{{ getPeriodLabel(period) }}</div>

													<div class="tasks-container">
														<!-- 处理上午实际任务 -->
														<template v-if="period === 'morning'">
															<template v-if="getActualFullDayTask(scope.row, dayData.key)">
																<div
																	class="task-item actual-task"
																	:style="{
																		backgroundColor: getActualTaskCardBgColor(getActualFullDayTask(scope.row, dayData.key)),
																		marginBottom: '-10px',
																	}"
																	@click.stop="
																		handleTaskClick(getActualFullDayTask(scope.row, dayData.key), scope.row, dayData.key, 'fullday', 'actual')
																	"
																>
																	<div class="demand-content">
																		<div class="demand-time-badge">
																			{{ formatTimeUnit(getActualFullDayTask(scope.row, dayData.key).timeUnit, 'fullday') }}
																		</div>
																		<div class="demand-description">{{ getActualFullDayTask(scope.row, dayData.key).description }}</div>
																	</div>
																</div>
															</template>
															<template v-else-if="getActualMorningTask(scope.row, dayData.key)">
																<div
																	class="task-item actual-task"
																	:style="{
																		backgroundColor: getActualTaskCardBgColor(getActualMorningTask(scope.row, dayData.key)),
																		marginBottom: '-10px',
																	}"
																	@click.stop="
																		handleTaskClick(getActualMorningTask(scope.row, dayData.key), scope.row, dayData.key, 'morning', 'actual')
																	"
																>
																	<div class="demand-content">
																		<div class="demand-time-badge">
																			{{ formatTimeUnit(getActualMorningTask(scope.row, dayData.key).timeUnit, 'morning') }}
																		</div>
																		<div class="demand-description">{{ getActualMorningTask(scope.row, dayData.key).description }}</div>
																	</div>
																</div>
															</template>
														</template>

														<!-- 处理下午实际任务 -->
														<template v-else-if="period === 'afternoon'">
															<template v-if="getActualFullDayTask(scope.row, dayData.key)">
																<div
																	class="task-item actual-task"
																	:style="{
																		backgroundColor: getActualTaskCardBgColor(getActualFullDayTask(scope.row, dayData.key)),
																		minHeight: '100%',
																	}"
																	@click.stop="
																		handleTaskClick(getActualFullDayTask(scope.row, dayData.key), scope.row, dayData.key, 'fullday', 'actual')
																	"
																>
																	<div class="demand-content">
																		<div class="demand-time-badge">{{ formatTimeUnit(getActualFullDayTask(scope.row, dayData.key).timeUnit) }}</div>
																		<div class="demand-description">{{ getActualFullDayTask(scope.row, dayData.key).description }}</div>
																	</div>
																</div>
															</template>
															<template v-else>
																<div class="afternoon-tasks-area">
																	<template v-if="getActualAfternoonFullTask(scope.row, dayData.key)">
																		<div
																			class="task-item actual-task"
																			:style="{
																				backgroundColor: getActualTaskCardBgColor(getActualAfternoonFullTask(scope.row, dayData.key)),
																				minHeight: '100%',
																			}"
																			@click.stop="
																				handleTaskClick(
																					getActualAfternoonFullTask(scope.row, dayData.key),
																					scope.row,
																					dayData.key,
																					'afternoon_full',
																					'actual'
																				)
																			"
																		>
																			<div class="demand-content">
																				<div class="demand-time-badge">
																					{{ formatTimeUnit(getActualAfternoonFullTask(scope.row, dayData.key).timeUnit, 'afternoon_full') }}
																				</div>
																				<div class="demand-description">{{ getActualAfternoonFullTask(scope.row, dayData.key).description }}</div>
																			</div>
																		</div>
																	</template>
																	<template v-else>
																		<div class="afternoon-split-area">
																			<div
																				class="afternoon-slot slot-14-16"
																				:class="{
																					'has-task': getActualAfternoonTask1(scope.row, dayData.key),
																					'drag-highlight': isDragHighlight(scope.row, dayData.key, 'afternoon_1'),
																				}"
																				@dragenter="handleDragEnter(scope.row, dayData.key, 'afternoon_1')"
																				@dragleave="handleDragLeave"
																			>
																				<div v-if="!getActualAfternoonTask1(scope.row, dayData.key)" class="time-slot-label">14:00-16:00</div>
																				<template v-if="getActualAfternoonTask1(scope.row, dayData.key)">
																					<div
																						:class="getActualTaskCardClass(getActualAfternoonTask1(scope.row, dayData.key)) + ' small-task'"
																						:style="{
																							backgroundColor: getActualTaskCardBgColor(getActualAfternoonTask1(scope.row, dayData.key)),
																						}"
																						@click.stop="
																							handleTaskClick(
																								getActualAfternoonTask1(scope.row, dayData.key),
																								scope.row,
																								dayData.key,
																								'afternoon_1',
																								'actual'
																							)
																						"
																					>
																						<div class="demand-content small">
																							<div class="demand-time-badge small">{{ formatTimeUnit('2', 'afternoon_1') }}</div>
																							<div class="demand-description small">
																								{{ getActualAfternoonTask1(scope.row, dayData.key).description }}
																							</div>
																						</div>
																					</div>
																				</template>
																			</div>
																			<div
																				class="afternoon-slot slot-16-18"
																				:class="{
																					'has-task': getActualAfternoonTask2(scope.row, dayData.key),
																					'drag-highlight': isDragHighlight(scope.row, dayData.key, 'afternoon_2'),
																				}"
																				@dragenter="handleDragEnter(scope.row, dayData.key, 'afternoon_2')"
																				@dragleave="handleDragLeave"
																			>
																				<div v-if="!getActualAfternoonTask2(scope.row, dayData.key)" class="time-slot-label">16:00-18:00</div>
																				<template v-if="getActualAfternoonTask2(scope.row, dayData.key)">
																					<div
																						:class="getActualTaskCardClass(getActualAfternoonTask2(scope.row, dayData.key)) + ' small-task'"
																						:style="{
																							backgroundColor: getActualTaskCardBgColor(getActualAfternoonTask2(scope.row, dayData.key)),
																						}"
																						@click.stop="
																							handleTaskClick(
																								getActualAfternoonTask2(scope.row, dayData.key),
																								scope.row,
																								dayData.key,
																								'afternoon_2',
																								'actual'
																							)
																						"
																					>
																						<div class="demand-content small">
																							<div class="demand-time-badge small">{{ formatTimeUnit('2', 'afternoon_2') }}</div>
																							<div class="demand-description small">
																								{{ getActualAfternoonTask2(scope.row, dayData.key).description }}
																							</div>
																						</div>
																					</div>
																				</template>
																			</div>
																		</div>
																	</template>
																</div>
															</template>
														</template>

														<!-- 晚上时间段的双卡片展示 -->
														<template v-else-if="period === 'evening'">
															<div class="task-cards-wrapper">
																<div
																	v-if="getActualEveningTask(scope.row, dayData.key)"
																	class="task-item actual-task"
																	:style="{
																		backgroundColor: getActualTaskCardBgColor(getActualEveningTask(scope.row, dayData.key)),
																		marginBottom: '-10px',
																	}"
																	@click.stop="
																		handleTaskClick(getActualEveningTask(scope.row, dayData.key), scope.row, dayData.key, 'evening', 'actual')
																	"
																>
																	<div class="demand-content">
																		<div class="demand-time-badge">{{ formatTimeUnit(getActualEveningTask(scope.row, dayData.key).timeUnit) }}</div>
																		<div class="demand-description">{{ getActualEveningTask(scope.row, dayData.key).description }}</div>
																	</div>
																</div>
															</div>
														</template>
													</div>
												</div>
											</template>
										</template>

										<!-- 原定&实际模式：左右分列展示 -->
										<template v-else-if="displayMode === 'both'">
											<div class="both-mode-container">
												<!-- 左侧：原定安排列 -->
												<div class="original-column">
													<!-- <div class="column-header">原定</div> -->
													<div class="column-content">
														<!-- 检查是否有全天任务，如果有则合并显示 -->
														<template
															v-if="
																getFullDayTask(scope.row, dayData.key) && visiblePeriods.includes('morning') && visiblePeriods.includes('afternoon')
															"
														>
															<!-- 全天任务合并展示区域 -->
															<div class="full-day-merged-container">
																<div
																	:class="getTaskCardClass(getFullDayTask(scope.row, dayData.key))"
																	class="full-day-merged-task"
																	:style="{
																		backgroundColor: getTaskCardBgColor(getFullDayTask(scope.row, dayData.key)),
																		minHeight: '100%',
																	}"
																	:draggable="isTaskDraggable(getFullDayTask(scope.row, dayData.key))"
																	@dragstart="handleTaskDragStart($event, getFullDayTask(scope.row, dayData.key), scope.row, dayData.key, 'fullday')"
																	@dragend="handleDragEnd"
																	@contextmenu.prevent="handleTaskContextMenu($event, scope.row, dayData.key, 'fullday')"
																	@click.stop="handleTaskClick(getFullDayTask(scope.row, dayData.key), scope.row, dayData.key, 'fullday', 'original')"
																>
																	<div class="demand-content">
																		<div class="demand-time-badge">
																			{{ formatTimeUnit(getFullDayTask(scope.row, dayData.key).timeUnit, 'fullday') }}
																		</div>
																		<div class="demand-description">{{ getTaskDisplayDescription(getFullDayTask(scope.row, dayData.key)) }}</div>
																	</div>
																</div>
															</div>

															<!-- 如果还有晚上时间段，单独处理晚上 -->
															<template v-if="visiblePeriods.includes('evening')">
																<div
																	class="time-period evening-period"
																	:class="{ 'drag-highlight': isDragHighlight(scope.row, dayData.key, 'evening') }"
																	@dragover.prevent
																	@drop="handleOriginalColumnDrop($event)"
																	@dragenter="handleDragEnter(scope.row, dayData.key, 'evening')"
																	@dragleave="handleDragLeave"
																>
																	<div v-if="!hasTaskInPeriod(scope.row, dayData.key, 'evening')" class="period-label">
																		{{ getPeriodLabel('evening') }}
																	</div>
																	<div class="tasks-container">
																		<template v-if="getEveningTask(scope.row, dayData.key)">
																			<div
																				:class="getTaskCardClass(getEveningTask(scope.row, dayData.key))"
																				:style="{
																					backgroundColor: getTaskCardBgColor(getEveningTask(scope.row, dayData.key)),
																					marginBottom: '-10px',
																				}"
																				:draggable="isTaskDraggable(getEveningTask(scope.row, dayData.key))"
																				@dragstart="
																					handleTaskDragStart($event, getEveningTask(scope.row, dayData.key), scope.row, dayData.key, 'evening')
																				"
																				@dragend="handleDragEnd"
																				@contextmenu.prevent="handleTaskContextMenu($event, scope.row, dayData.key, 'evening')"
																			>
																				<div class="demand-content">
																					<div class="demand-time-badge">
																						{{ formatTimeUnit(getEveningTask(scope.row, dayData.key).timeUnit, 'evening') }}
																					</div>
																					<div class="demand-description">{{ getEveningTask(scope.row, dayData.key).description }}</div>
																				</div>
																			</div>
																		</template>
																	</div>
																</div>
															</template>
														</template>

														<!-- 常规时间段展示 -->
														<template v-else>
															<div
																class="time-period"
																v-for="(period, index) in visiblePeriods"
																:key="index"
																@dragover.prevent
																@drop="handleOriginalColumnDrop($event)"
																@dragenter="handleDragEnter(scope.row, dayData.key, period)"
																@dragleave="handleDragLeave"
																:class="[getPeriodClass(period), { 'drag-highlight': isDragHighlight(scope.row, dayData.key, period) }]"
															>
																<div v-if="!hasTaskInPeriod(scope.row, dayData.key, period)" class="period-label">{{ getPeriodLabel(period) }}</div>
																<div class="tasks-container">
																	<!-- 处理上午时间段 -->
																	<template v-if="period === 'morning'">
																		<template v-if="getFullDayTask(scope.row, dayData.key)">
																			<div
																				:class="getTaskCardClass(getFullDayTask(scope.row, dayData.key))"
																				:style="{
																					backgroundColor: getTaskCardBgColor(getFullDayTask(scope.row, dayData.key)),
																					marginBottom: '-10px',
																				}"
																				:draggable="isTaskDraggable(getFullDayTask(scope.row, dayData.key))"
																				@dragstart="handleTaskDragStart($event, getFullDayTask(scope.row, dayData.key), scope.row, dayData.key, 'fullday')"
																				@dragend="handleDragEnd"
																				@contextmenu.prevent="handleTaskContextMenu($event, scope.row, dayData.key, 'fullday')"
																				@click.stop="handleTaskClick(getFullDayTask(scope.row, dayData.key), scope.row, dayData.key, 'fullday', 'original')"
																			>
																				<div class="demand-content">
																					<div class="demand-time-badge">
																						{{ formatTimeUnit(getFullDayTask(scope.row, dayData.key).timeUnit, 'fullday') }}
																					</div>
																					<div class="demand-description">{{ getFullDayTask(scope.row, dayData.key).description }}</div>
																				</div>
																			</div>
																		</template>
																		<template v-else-if="getMorningTask(scope.row, dayData.key)">
																			<div
																				:class="getTaskCardClass(getMorningTask(scope.row, dayData.key))"
																				:style="{
																					backgroundColor: getTaskCardBgColor(getMorningTask(scope.row, dayData.key)),
																					marginBottom: '-10px',
																				}"
																				:draggable="isTaskDraggable(getMorningTask(scope.row, dayData.key))"
																				@dragstart="handleTaskDragStart($event, getMorningTask(scope.row, dayData.key), scope.row, dayData.key, 'morning')"
																				@dragend="handleDragEnd"
																				@contextmenu.prevent="handleTaskContextMenu($event, scope.row, dayData.key, 'morning')"
																				@click.stop="handleTaskClick(getMorningTask(scope.row, dayData.key), scope.row, dayData.key, 'morning', 'original')"
																			>
																				<div class="demand-content">
																					<div class="demand-time-badge">
																						{{ formatTimeUnit(getMorningTask(scope.row, dayData.key).timeUnit, 'morning') }}
																					</div>
																					<div class="demand-description">{{ getMorningTask(scope.row, dayData.key).description }}</div>
																				</div>
																			</div>
																		</template>
																	</template>

																	<!-- 处理下午时间段 -->
																	<template v-else-if="period === 'afternoon'">
																		<template v-if="getFullDayTask(scope.row, dayData.key)">
																			<div
																				:class="getTaskCardClass(getFullDayTask(scope.row, dayData.key))"
																				:style="{
																					backgroundColor: getTaskCardBgColor(getFullDayTask(scope.row, dayData.key)),
																				}"
																				@click.stop="
																					handleTaskClick(getFullDayTask(scope.row, dayData.key), scope.row, dayData.key, 'fullday', 'original')
																				"
																			>
																				<div class="demand-content">
																					<div class="demand-time-badge">
																						{{ formatTimeUnit(getFullDayTask(scope.row, dayData.key).timeUnit, 'fullday') }}
																					</div>
																					<div class="demand-description">{{ getFullDayTask(scope.row, dayData.key).description }}</div>
																				</div>
																			</div>
																		</template>
																		<template v-else>
																			<div class="afternoon-tasks-area">
																				<template v-if="getAfternoonFullTask(scope.row, dayData.key)">
																					<div
																						:class="getTaskCardClass(getAfternoonFullTask(scope.row, dayData.key))"
																						:style="{
																							backgroundColor: getTaskCardBgColor(getAfternoonFullTask(scope.row, dayData.key)),
																							minHeight: '100%',
																						}"
																						:draggable="isTaskDraggable(getAfternoonFullTask(scope.row, dayData.key))"
																						@dragstart="
																							handleTaskDragStart(
																								$event,
																								getAfternoonFullTask(scope.row, dayData.key),
																								scope.row,
																								dayData.key,
																								'afternoon_full'
																							)
																						"
																						@dragend="handleDragEnd"
																						@contextmenu.prevent="handleTaskContextMenu($event, scope.row, dayData.key, 'afternoon_full')"
																						@click.stop="
																							handleTaskClick(
																								getAfternoonFullTask(scope.row, dayData.key),
																								scope.row,
																								dayData.key,
																								'afternoon_full',
																								'original'
																							)
																						"
																					>
																						<div class="demand-content">
																							<div class="demand-time-badge">
																								{{ formatTimeUnit(getAfternoonFullTask(scope.row, dayData.key).timeUnit, 'afternoon_full') }}
																							</div>
																							<div class="demand-description">{{ getAfternoonFullTask(scope.row, dayData.key).description }}</div>
																						</div>
																					</div>
																				</template>
																				<template v-else>
																					<div class="afternoon-split-area">
																						<div
																							class="afternoon-slot slot-14-16"
																							:class="{
																								'has-task': getAfternoonTask1(scope.row, dayData.key),
																								'drag-highlight': isDragHighlight(scope.row, dayData.key, 'afternoon_1'),
																							}"
																							@dragover.prevent
																							@drop="handleOriginalColumnDrop($event)"
																							@dragenter="handleDragEnter(scope.row, dayData.key, 'afternoon_1')"
																							@dragleave="handleDragLeave"
																						>
																							<div v-if="!getAfternoonTask1(scope.row, dayData.key)" class="time-slot-label">14:00-16:00</div>
																							<template v-if="getAfternoonTask1(scope.row, dayData.key)">
																								<div
																									:class="getTaskCardClass(getAfternoonTask1(scope.row, dayData.key)) + ' small-task'"
																									:style="{
																										backgroundColor: getTaskCardBgColor(getAfternoonTask1(scope.row, dayData.key)),
																									}"
																									:draggable="isTaskDraggable(getAfternoonTask1(scope.row, dayData.key))"
																									@dragstart="
																										handleTaskDragStart(
																											$event,
																											getAfternoonTask1(scope.row, dayData.key),
																											scope.row,
																											dayData.key,
																											'afternoon_1'
																										)
																									"
																									@dragend="handleDragEnd"
																									@contextmenu.prevent="handleTaskContextMenu($event, scope.row, dayData.key, 'afternoon_1')"
																									@click.stop="
																										handleTaskClick(
																											getAfternoonTask1(scope.row, dayData.key),
																											scope.row,
																											dayData.key,
																											'afternoon_1',
																											'original'
																										)
																									"
																								>
																									<div class="demand-content small">
																										<div class="demand-time-badge small">{{ formatTimeUnit('2', 'afternoon_1') }}</div>
																										<div class="demand-description small">
																											{{ getAfternoonTask1(scope.row, dayData.key).description }}
																										</div>
																									</div>
																								</div>
																							</template>
																						</div>
																						<div
																							class="afternoon-slot slot-16-18"
																							:class="{
																								'has-task': getAfternoonTask2(scope.row, dayData.key),
																								'drag-highlight': isDragHighlight(scope.row, dayData.key, 'afternoon_2'),
																							}"
																							@dragover.prevent
																							@drop="handleOriginalColumnDrop($event)"
																							@dragenter="handleDragEnter(scope.row, dayData.key, 'afternoon_2')"
																							@dragleave="handleDragLeave"
																						>
																							<div v-if="!getAfternoonTask2(scope.row, dayData.key)" class="time-slot-label">16:00-18:00</div>
																							<template v-if="getAfternoonTask2(scope.row, dayData.key)">
																								<div
																									:class="getTaskCardClass(getAfternoonTask2(scope.row, dayData.key)) + ' small-task'"
																									:style="{
																										backgroundColor: getTaskCardBgColor(getAfternoonTask2(scope.row, dayData.key)),
																									}"
																									:draggable="isTaskDraggable(getAfternoonTask2(scope.row, dayData.key))"
																									@dragstart="
																										handleTaskDragStart(
																											$event,
																											getAfternoonTask2(scope.row, dayData.key),
																											scope.row,
																											dayData.key,
																											'afternoon_2'
																										)
																									"
																									@dragend="handleDragEnd"
																									@contextmenu.prevent="handleTaskContextMenu($event, scope.row, dayData.key, 'afternoon_2')"
																									@click.stop="
																										handleTaskClick(
																											getAfternoonTask2(scope.row, dayData.key),
																											scope.row,
																											dayData.key,
																											'afternoon_2',
																											'original'
																										)
																									"
																								>
																									<div class="demand-content small">
																										<div class="demand-time-badge small">{{ formatTimeUnit('2', 'afternoon_2') }}</div>
																										<div class="demand-description small">
																											{{ getAfternoonTask2(scope.row, dayData.key).description }}
																										</div>
																									</div>
																								</div>
																							</template>
																						</div>
																					</div>
																				</template>
																			</div>
																		</template>
																	</template>

																	<!-- 处理晚上时间段 -->
																	<template v-else-if="period === 'evening'">
																		<template v-if="getEveningTask(scope.row, dayData.key)">
																			<div
																				:class="getTaskCardClass(getEveningTask(scope.row, dayData.key))"
																				:style="{
																					backgroundColor: getTaskCardBgColor(getEveningTask(scope.row, dayData.key)),
																					marginBottom: '-10px',
																				}"
																				:draggable="isTaskDraggable(getEveningTask(scope.row, dayData.key))"
																				@dragstart="handleTaskDragStart($event, getEveningTask(scope.row, dayData.key), scope.row, dayData.key, 'evening')"
																				@dragend="handleDragEnd"
																				@contextmenu.prevent="handleTaskContextMenu($event, scope.row, dayData.key, 'evening')"
																				@click.stop="handleTaskClick(getEveningTask(scope.row, dayData.key), scope.row, dayData.key, 'evening', 'original')"
																			>
																				<div class="demand-content">
																					<div class="demand-time-badge">
																						{{ formatTimeUnit(getEveningTask(scope.row, dayData.key).timeUnit, 'evening') }}
																					</div>
																					<div class="demand-description">{{ getEveningTask(scope.row, dayData.key).description }}</div>
																				</div>
																			</div>
																		</template>
																	</template>
																</div>
															</div>
														</template>
													</div>
												</div>

												<!-- 右侧：实际执行列 -->
												<div class="actual-column">
													<!-- <div class="column-header">实际</div> -->
													<div class="column-content">
														<!-- 检查是否有全天实际任务，如果有则合并显示 -->
														<template
															v-if="
																getActualFullDayTask(scope.row, dayData.key) &&
																visiblePeriods.includes('morning') &&
																visiblePeriods.includes('afternoon')
															"
														>
															<!-- 全天实际任务合并展示区域 -->
															<div class="full-day-merged-container">
																<div
																	class="task-item full-day-merged-task actual-task"
																	:style="{
																		backgroundColor: getActualTaskCardBgColor(getActualFullDayTask(scope.row, dayData.key)),
																		minHeight: '100%',
																	}"
																	@click.stop="
																		handleTaskClick(getActualFullDayTask(scope.row, dayData.key), scope.row, dayData.key, 'fullday', 'actual')
																	"
																>
																	<div class="demand-content">
																		<div class="demand-time-badge">
																			{{ formatTimeUnit(getActualFullDayTask(scope.row, dayData.key).timeUnit, 'fullday') }}
																		</div>
																		<div class="demand-description">{{ getActualFullDayTask(scope.row, dayData.key).description }}</div>
																	</div>
																</div>
															</div>

															<!-- 如果还有晚上时间段，单独处理晚上 -->
															<template v-if="visiblePeriods.includes('evening')">
																<div
																	class="time-period evening-period"
																	:class="{ 'drag-highlight': isDragHighlight(scope.row, dayData.key, 'evening_actual') }"
																	@dragenter="handleDragEnter(scope.row, dayData.key, 'evening_actual')"
																	@dragleave="handleDragLeave"
																>
																	<div v-if="!getActualEveningTask(scope.row, dayData.key)" class="period-label">{{ getPeriodLabel('evening') }}</div>
																	<div class="tasks-container">
																		<template v-if="getActualEveningTask(scope.row, dayData.key)">
																			<div
																				class="task-item actual-task"
																				:style="{ backgroundColor: getActualTaskCardBgColor(getActualEveningTask(scope.row, dayData.key)) }"
																				@click.stop="
																					handleTaskClick(getActualEveningTask(scope.row, dayData.key), scope.row, dayData.key, 'evening', 'actual')
																				"
																			>
																				<div class="demand-content">
																					<div class="demand-time-badge">
																						{{ formatTimeUnit(getActualEveningTask(scope.row, dayData.key).timeUnit, 'evening') }}
																					</div>
																					<div class="demand-description">{{ getActualEveningTask(scope.row, dayData.key).description }}</div>
																				</div>
																			</div>
																		</template>
																	</div>
																</div>
															</template>
														</template>

														<!-- 常规实际任务时间段展示 -->
														<template v-else>
															<div
																class="time-period"
																v-for="(period, index) in visiblePeriods"
																:key="index"
																:class="[getPeriodClass(period), { 'drag-highlight': isDragHighlight(scope.row, dayData.key, period) }]"
																@dragenter="handleDragEnter(scope.row, dayData.key, period)"
																@dragleave="handleDragLeave"
															>
																<div v-if="!hasActualTaskInPeriod(scope.row, dayData.key, period)" class="period-label">
																	{{ getPeriodLabel(period) }}
																</div>
																<div class="tasks-container">
																	<!-- 处理上午实际任务 -->
																	<template v-if="period === 'morning'">
																		<template v-if="getActualFullDayTask(scope.row, dayData.key)">
																			<div
																				class="task-item actual-task"
																				:style="{
																					backgroundColor: getActualTaskCardBgColor(getActualFullDayTask(scope.row, dayData.key)),
																					marginBottom: '-10px',
																				}"
																				@click.stop="
																					handleTaskClick(getActualFullDayTask(scope.row, dayData.key), scope.row, dayData.key, 'fullday', 'actual')
																				"
																			>
																				<div class="demand-content">
																					<div class="demand-time-badge">
																						{{ formatTimeUnit(getActualFullDayTask(scope.row, dayData.key).timeUnit, 'fullday') }}
																					</div>
																					<div class="demand-description">{{ getActualFullDayTask(scope.row, dayData.key).description }}</div>
																				</div>
																			</div>
																		</template>
																		<template v-else-if="getActualMorningTask(scope.row, dayData.key)">
																			<div
																				class="task-item actual-task"
																				:style="{
																					backgroundColor: getActualTaskCardBgColor(getActualMorningTask(scope.row, dayData.key)),
																					marginBottom: '-10px',
																				}"
																				@click.stop="
																					handleTaskClick(getActualMorningTask(scope.row, dayData.key), scope.row, dayData.key, 'morning', 'actual')
																				"
																			>
																				<div class="demand-content">
																					<div class="demand-time-badge">
																						{{ formatTimeUnit(getActualMorningTask(scope.row, dayData.key).timeUnit, 'morning') }}
																					</div>
																					<div class="demand-description">{{ getActualMorningTask(scope.row, dayData.key).description }}</div>
																				</div>
																			</div>
																		</template>
																	</template>

																	<!-- 处理下午实际任务 -->
																	<template v-else-if="period === 'afternoon'">
																		<template v-if="getActualFullDayTask(scope.row, dayData.key)">
																			<div
																				class="task-item actual-task"
																				:style="{ backgroundColor: getActualTaskCardBgColor(getActualFullDayTask(scope.row, dayData.key)) }"
																				@click.stop="
																					handleTaskClick(getActualFullDayTask(scope.row, dayData.key), scope.row, dayData.key, 'fullday', 'actual')
																				"
																			>
																				<div class="demand-content">
																					<div class="demand-time-badge">
																						{{ formatTimeUnit(getActualFullDayTask(scope.row, dayData.key).timeUnit, 'fullday') }}
																					</div>
																					<div class="demand-description">{{ getActualFullDayTask(scope.row, dayData.key).description }}</div>
																				</div>
																			</div>
																		</template>
																		<template v-else>
																			<div class="afternoon-tasks-area">
																				<template v-if="getActualAfternoonFullTask(scope.row, dayData.key)">
																					<div
																						class="task-item actual-task"
																						:style="{
																							backgroundColor: getActualTaskCardBgColor(getActualAfternoonFullTask(scope.row, dayData.key)),
																							minHeight: '100%',
																						}"
																						@click.stop="
																							handleTaskClick(
																								getActualAfternoonFullTask(scope.row, dayData.key),
																								scope.row,
																								dayData.key,
																								'afternoon_full',
																								'actual'
																							)
																						"
																					>
																						<div class="demand-content">
																							<div class="demand-time-badge">
																								{{ formatTimeUnit(getActualAfternoonFullTask(scope.row, dayData.key).timeUnit, 'afternoon_full') }}
																							</div>
																							<div class="demand-description">{{ getActualAfternoonFullTask(scope.row, dayData.key).description }}</div>
																						</div>
																					</div>
																				</template>
																				<template v-else>
																					<div class="afternoon-split-area">
																						<div
																							class="afternoon-slot slot-14-16"
																							:class="{
																								'has-task': getActualAfternoonTask1(scope.row, dayData.key),
																								'drag-highlight': isDragHighlight(scope.row, dayData.key, 'afternoon_1_actual'),
																							}"
																							@dragenter="handleDragEnter(scope.row, dayData.key, 'afternoon_1_actual')"
																							@dragleave="handleDragLeave"
																						>
																							<div v-if="!getActualAfternoonTask1(scope.row, dayData.key)" class="time-slot-label">14:00-16:00</div>
																							<template v-if="getActualAfternoonTask1(scope.row, dayData.key)">
																								<div
																									class="task-item small-task actual-task"
																									:style="{
																										backgroundColor: getActualTaskCardBgColor(getActualAfternoonTask1(scope.row, dayData.key)),
																									}"
																									@click.stop="
																										handleTaskClick(
																											getActualAfternoonTask1(scope.row, dayData.key),
																											scope.row,
																											dayData.key,
																											'afternoon_1',
																											'actual'
																										)
																									"
																								>
																									<div class="demand-content small">
																										<div class="demand-time-badge small">{{ formatTimeUnit('2', 'afternoon_1') }}</div>
																										<div class="demand-description small">
																											{{ getActualAfternoonTask1(scope.row, dayData.key).description }}
																										</div>
																									</div>
																								</div>
																							</template>
																						</div>
																						<div
																							class="afternoon-slot slot-16-18"
																							:class="{
																								'has-task': getActualAfternoonTask2(scope.row, dayData.key),
																								'drag-highlight': isDragHighlight(scope.row, dayData.key, 'afternoon_2_actual'),
																							}"
																							@dragenter="handleDragEnter(scope.row, dayData.key, 'afternoon_2_actual')"
																							@dragleave="handleDragLeave"
																						>
																							<div v-if="!getActualAfternoonTask2(scope.row, dayData.key)" class="time-slot-label">16:00-18:00</div>
																							<template v-if="getActualAfternoonTask2(scope.row, dayData.key)">
																								<div
																									class="task-item small-task actual-task"
																									:style="{
																										backgroundColor: getActualTaskCardBgColor(getActualAfternoonTask2(scope.row, dayData.key)),
																									}"
																									@click.stop="
																										handleTaskClick(
																											getActualAfternoonTask2(scope.row, dayData.key),
																											scope.row,
																											dayData.key,
																											'afternoon_2',
																											'actual'
																										)
																									"
																								>
																									<div class="demand-content small">
																										<div class="demand-time-badge small">{{ formatTimeUnit('2', 'afternoon_2') }}</div>
																										<div class="demand-description small">
																											{{ getActualAfternoonTask2(scope.row, dayData.key).description }}
																										</div>
																									</div>
																								</div>
																							</template>
																						</div>
																					</div>
																				</template>
																			</div>
																		</template>
																	</template>

																	<!-- 处理晚上实际任务 -->
																	<template v-else-if="period === 'evening'">
																		<template v-if="getActualEveningTask(scope.row, dayData.key)">
																			<div
																				class="task-item actual-task"
																				:style="{
																					backgroundColor: getActualTaskCardBgColor(getActualEveningTask(scope.row, dayData.key)),
																					marginBottom: '-10px',
																				}"
																				@click.stop="
																					handleTaskClick(getActualEveningTask(scope.row, dayData.key), scope.row, dayData.key, 'evening', 'actual')
																				"
																			>
																				<div class="demand-content">
																					<div class="demand-time-badge">
																						{{ formatTimeUnit(getActualEveningTask(scope.row, dayData.key).timeUnit, 'evening') }}
																					</div>
																					<div class="demand-description">{{ getActualEveningTask(scope.row, dayData.key).description }}</div>
																				</div>
																			</div>
																		</template>
																	</template>
																</div>
															</div>
														</template>
													</div>
												</div>
											</div>
										</template>
									</div>
								</template>
							</el-table-column>
						</el-table>
					</div>
				</div>
			</div>
		</div>
	</div>

	<!-- 编辑需求弹窗 -->
	<!-- <DemandEditDialog
		v-model:visible="editDialogVisible"
		:demand="currentEditDemand"
		:personal-demands="personalDemands"
		@update:personal-demands="handleEditSubmit"
	/> -->
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { Box, ArrowUp, ArrowDown, DocumentCopy, Delete } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { useDemandPoolApi } from '/@/api/demandPool';
import { useUserApi } from '/@/api/user';
const { getUserGroup, getCharacteristicGroup } = useUserApi();
const { deleteDemandPool, copyDemandPool, demandPoolWorkMoveIntoTable } = useDemandPoolApi();
// import DemandEditDialog from './DemandEditDialog.vue';

// Props
interface Props {
	personalDemands: any[];
	tableData: any[];
	filterForm: any;
	weekDatesData: any[];
	weekDateRange: {
		startDate: string;
		endDate: string;
	};
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits([
	'update-table-data',
	'update-personal-demands',
	'edit-demand',
	'copy-demand',
	'change-week',
	'filter-change',
	'task-click',
]);

// 本地筛选状态
const localFilterForm = ref({
	workType: 'all',
	business: '',
	person: [] as string[],
	businessOptions: [] as Array<{ value: string; label: string }>,
});

// 级联选择器配置
const cascaderProps = {
	multiple: true,
	checkStrictly: false, // 保持原有逻辑，父子关联
	value: 'id',
	label: 'name',
	children: 'users',
	disabled: 'disabled', // 启用禁用功能
	emitPath: false,
	expandTrigger: 'hover',
};

// 级联选择器选项数据
const cascaderOptions = ref<any[]>([]);

// 初始化业务数据
const initBusinessData = async () => {
	try {
		const response = await getUserGroup();
		if (response.code === 200 && response.data) {
			// 提取业务选项
			const businessOptions = response.data.map((business: any) => ({
				value: business.id,
				label: business.name,
			}));
			localFilterForm.value.businessOptions = businessOptions;
		}
	} catch (error) {
		// 获取业务数据失败
		ElMessage.error('获取业务数据失败');
	}
};

// 获取特性组数据
const getCharacteristicGroupData = async () => {
	try {
		const res = await getCharacteristicGroup({ is_leader: 0 });
		if (res?.code === 200) {
			// 处理数据格式
			const options = res.data.map((group: any) => {
				const filteredUsers = group.users
					.filter((user: any) => !user.is_delete && user.is_test) // 过滤掉已删除的用户和未完成测试的用户
					.map((user: any) => ({
						id: user.id,
						name: user.real_name || user.username
					}));
				
				return {
					id: group.id || group.name,
					name: group.name,
					users: filteredUsers,
					disabled: filteredUsers.length === 0 // 没有子用户时禁用checkbox
				};
			}).sort((a: any, b: any) => {
				// 有子用户的排在前面，没有子用户的排在后面
				if (a.users.length > 0 && b.users.length === 0) return -1;
				if (a.users.length === 0 && b.users.length > 0) return 1;
				return 0; // 保持原有顺序
			});
			cascaderOptions.value = options;
		}
	} catch (error) {
		// 获取特性组数据失败
		ElMessage.error('获取特性组数据失败');
	}
};

// 清理右键菜单的函数
const clearContextMenus = () => {
	const existingMenus = document.querySelectorAll('.task-context-menu');
	existingMenus.forEach(menu => {
		if (menu.parentNode) {
			menu.parentNode.removeChild(menu);
		}
	});
};

// 全局拖拽监听器
const globalDragOverHandler = (event: DragEvent) => {
	// 只有在拖拽删除状态下才更新位置
	if (isDraggingForDelete.value) {
		updateDeleteZoneVisibility(event.clientY);
	}
};

// 组件挂载时初始化数据
onMounted(() => {
	initBusinessData();
	getCharacteristicGroupData(); // 获取级联选择器数据
	
	// 添加全局拖拽监听器
	document.addEventListener('dragover', globalDragOverHandler);
});

// 组件卸载时清理所有菜单
onUnmounted(() => {
	clearContextMenus();
	
	// 移除全局拖拽监听器
	document.removeEventListener('dragover', globalDragOverHandler);
});

// 获取日期中文标签的函数
const getDayLabel = (day: string) => {
	// 从 weekDatesData 中获取对应的中文标签
	const dayData = props.weekDatesData.find((item) => item.key === day);
	return dayData?.label || day;
};

// 判断是否是今天
const isToday = (dayData: any) => {
	const today = new Date().toISOString().split('T')[0]; // 获取今天的日期 YYYY-MM-DD
	return dayData.fullDateStr === today;
};

const displayMode = ref('original');

// 编辑弹窗相关
// const editDialogVisible = ref(false);
// const currentEditDemand = ref(null);

// 拖拽状态跟踪
const isDragging = ref(false);

// 拖拽高亮状态跟踪
const dragHighlightTarget = ref<string>('');

// 当前拖拽的数据
const currentDragData = ref<any>(null);

// 拖拽验证状态跟踪 - 'valid'(绿色) | 'invalid'(红色) | ''(无高亮)
const dragValidationStatus = ref<string>('');

// 全天任务拖拽高亮状态跟踪
const fullDayHighlightTarget = ref<string>('');

// 防抖验证计时器
let validationTimer: NodeJS.Timeout | null = null;

// 只看自己的开关状态
const onlyShowSelf = ref(false);

// 任务复用模式开关状态
const taskCopyMode = ref(false);

// 需求池展开收起状态
const demandPoolExpanded = ref(true);

// 类别拖拽相关状态
const categoryDragHighlight = ref<string>('');
const currentDraggedDemand = ref<any>(null);

// 删除功能相关状态
const isDraggingForDelete = ref(false);
const isDragOverDelete = ref(false);
const deleteZoneVisible = ref(false);
const currentDeleteItem = ref<any>(null);
const dragStartY = ref<number>(0); // 拖拽开始时的Y坐标
const currentMouseY = ref<number>(0); // 当前鼠标Y坐标
const topEdgeThreshold = 80; // 顶部边缘阈值(px)

// 需求类别配置
const demandCategories = [
	{ label: '性能', value: 'perf' },
	{ label: '功能', value: 'func' },
	{ label: '协议', value: 'agree' },
	{ label: '开发', value: 'dev' },
	{ label: '公共', value: 'public' },
	{ label: '弱网', value: 'net' },
];

// 计算属性
const visiblePeriods = computed(() => {
	if (localFilterForm.value.workType === 'normal') {
		return ['morning', 'afternoon'];
	} else if (localFilterForm.value.workType === 'overtime') {
		return ['evening'];
	}
	return ['morning', 'afternoon', 'evening'];
});

const filteredTableData = computed(() => {
	return props.tableData;
});

// 删除区域样式计算属性
const deleteZoneClasses = computed(() => ({
	'delete-zone-visible': deleteZoneVisible.value,
	'delete-zone-active': isDraggingForDelete.value,
	'delete-zone-hover': isDragOverDelete.value
}));

// 判断当前拖拽项是否可删除
const canDelete = computed(() => {
	if (!currentDeleteItem.value) return false;
	
	// 需求池项目：都可删除
	if (currentDeleteItem.value.isFromDemandPool) return true;
	
	// 任务表格项目：检查权限
	// return currentDeleteItem.value.createdByCurrentUser && !currentDeleteItem.value.isExpired;
	return !currentDeleteItem.value.isExpired;

});

// 动态计算表格单元格高度
const taskCellHeight = computed(() => {
	const periodCount = visiblePeriods.value.length;
	// 定义基础高度：下午格子为标准高度，上午和晚上为60%
	const afternoonHeight = 65; // 下午格子标准高度
	const otherHeight = Math.round(afternoonHeight * 0.6); // 上午和晚上格子高度（60%）

	// both模式下不需要额外的header高度补偿，因为column header已被注释掉
	const headerHeight = 0; // 移除column header的高度补偿

	let baseHeight = 0;
	if (periodCount === 1 && visiblePeriods.value.includes('afternoon')) {
		// 只显示下午
		baseHeight = afternoonHeight;
	} else if (periodCount === 1) {
		// 只显示上午或晚上
		baseHeight = otherHeight;
	} else if (periodCount === 2 && visiblePeriods.value.includes('afternoon')) {
		// 上午+下午 或 下午+晚上
		baseHeight = otherHeight + afternoonHeight + 1; // +1为分隔线
	} else if (periodCount === 2) {
		// 其他两个时间段组合（理论上不存在，但保险起见）
		baseHeight = otherHeight * 2 + 1;
	} else if (periodCount === 3) {
		// 上午+下午+晚上
		baseHeight = otherHeight * 2 + afternoonHeight + 2; // +2为分隔线
	} else {
		// 默认情况
		baseHeight = periodCount * 60;
	}

	return `${baseHeight + headerHeight}px`;
});

// 方法定义
const getDemandsByCategory = (category: string) => {
	return props.personalDemands.filter((demand) => demand.category === category);
};

const formatTimeUnit = (timeUnit: string, period?: string) => {
	const timeMap: Record<string, string> = {
		// '2': '2h',
		// '4': '4h',
		'2': '上午(全时段)/下午(2h)',
		'4': '下午(全时段)/晚上(加班)',
		'6': '全天',
	};

	const baseTime = timeMap[timeUnit] || timeUnit;

	// 如果没有传入时间段，返回原始格式
	if (!period) {
		return baseTime;
	}

	// 根据时间段添加前缀
	const periodMap: Record<string, string> = {
		morning: '上午',
		afternoon_full: '下午',
		afternoon_1: '下午(2h)',
		afternoon_2: '下午(2h)',
		afternoon: '下午',
		evening: '加班',
		fullday: '全天',
	};

	const periodLabel = periodMap[period];
	if (periodLabel) {
		// 全天任务显示为 "全天(6h)"
		// if (period === 'fullday') {
		// 	// return `${periodLabel}(${baseTime})`;
		// 	return `${periodLabel}`;
		// }
		// 其他时间段显示为 "上午(2h)"、"下午(4h)"、"加班(4h)" 等
		return `${periodLabel}`;
	}

	return baseTime;
};

// 格式化需求池时间单位显示（支持多行显示）
const formatDemandTimeUnit = (timeUnit: string) => {
	const timeMap: Record<string, string> = {
		'2': '上午(全时段)/下午(2h)',
		'4': '下午(全时段)/晚上(加班)',
		'6': '全天',
	};

	const timeText = timeMap[timeUnit] || timeUnit;

	// 如果包含斜杠，则分割成两行
	if (timeText.includes('/')) {
		const parts = timeText.split('/');
		return {
			isMultiLine: true,
			firstLine: parts[0].trim(),
			secondLine: parts[1].trim(),
		};
	}

	// 单行显示
	return {
		isMultiLine: false,
		singleLine: timeText,
	};
};

const getTaskBgColor = (type: string) => {
	const bgColor: Record<string, string> = {
		perf: '#ffcdd2',
		func: '#c8e6c9',
		agree: '#fff3e0',
		dev: '#b3e5fc',
		public: '#d1c4e9',
		net: '#ffecb3',
	};
	return bgColor[type] || '#f5f5f5';
};

const getPeriodLabel = (period: string) => {
	const labels: Record<string, string> = {
		morning: '上午',
		afternoon:"",
		evening: '晚上',
	};
	return labels[period] || '';
};

const getRowKey = (row: any) => {
	return row.personId;
};

// 初始化拖拽删除功能
const initDragToDelete = (item: any, startY: number) => {
	// 设置当前删除项和拖拽状态
	currentDeleteItem.value = item;
	isDraggingForDelete.value = true;
	dragStartY.value = startY;
	deleteZoneVisible.value = false; // 初始不显示，需要拖拽到顶部才显示
};

// 更新鼠标位置并检查是否应该显示删除区域
const updateDeleteZoneVisibility = (mouseY: number) => {
	currentMouseY.value = mouseY;
	
	// 只有在拖拽状态下才检查
	if (!isDraggingForDelete.value) return;
	
	// 当鼠标拖拽到顶部边缘时显示删除区域
	if (mouseY <= topEdgeThreshold) {
		deleteZoneVisible.value = true;
	} else {
		deleteZoneVisible.value = false;
	}
};

// 需求池内部拖拽开始处理
const handleDemandDragStart = (event: DragEvent, demand: any) => {
	isDragging.value = true;
	currentDraggedDemand.value = demand;
	currentDragData.value = demand;
	
	// 初始化拖拽删除功能
	initDragToDelete({ ...demand, isFromDemandPool: true }, event.clientY);
	
	if (event.dataTransfer) {
		// 标记这是需求池内部拖拽
		const dragData = { ...demand, isFromDemandPool: true };
		event.dataTransfer.setData('text/plain', JSON.stringify(dragData));
	}
};


// 任务表格拖拽前处理数据
const handleTaskDragStart = (event: DragEvent, task: any, row: any, day: string, period: string) => {
	// 检查是否过期
	if (task.isExpired && !taskCopyMode.value) {
		event.preventDefault();
		ElMessage.warning('当前模式下，任务已过时效期，无法进行操作,如有需要可切换为复用模式');
		return;
	}

	// 检查是否为非自己创建的任务
	if (!task.createdByCurrentUser && !taskCopyMode.value) {
		event.preventDefault();
		ElMessage.warning('当前模式下不支持直接修改其他人的任务安排,如有需要可先删除后重新安排任务~');
		return;
	}

	// 原定&实际模式下不允许拖拽原定安排
	if (displayMode.value === 'both') {
		event.preventDefault();
		ElMessage.warning('原定&实际模式下，原定安排不允许拖拽操作');
		return;
	}

	// 初始化拖拽删除功能
	initDragToDelete({ ...task, isFromTable: true, createdByCurrentUser: task.createdByCurrentUser, isExpired: task.isExpired }, event.clientY);

	const dragData = {
		...task,
		isFromTable: true,
		sourcePersonId: row.personId,
		sourceDay: day,
		sourcePeriod: period,
	};

	// 存储当前拖拽的数据，用于拖拽验证
	currentDragData.value = dragData;

	if (event.dataTransfer) {
		event.dataTransfer.setData('text/plain', JSON.stringify(dragData));
	}
};
// 原定列拖入处理（禁用并提示）
const handleOriginalColumnDrop = (event: DragEvent) => {
	event.preventDefault();
	
	// 隐藏删除区域 - 拖拽到原定列时
	isDraggingForDelete.value = false;
	isDragOverDelete.value = false;
	deleteZoneVisible.value = false;
	currentDeleteItem.value = null;
	
	ElMessage.warning('原定&实际模式下，原定安排不允许拖入操作');
};

// 拖拽进入区域高亮
const handleDragEnter = (row: any, day: string, period: string) => {
	// 清理之前的计时器
	if (validationTimer) {
		clearTimeout(validationTimer);
		validationTimer = null;
	}

	// 处理需求池等特殊区域（row为null的情况）
	if (!row) {
		const targetKey = `${day}-${period}`;
		dragHighlightTarget.value = targetKey;
		fullDayHighlightTarget.value = '';
		
		// 检查拖拽到需求池的有效性
		if (currentDragData.value?.isFromTable) {
			// 检查是否为他人创建的任务（如果 createdByCurrentUser 不存在，默认认为是自己的任务）
			const isOwnTask = currentDragData.value.createdByCurrentUser !== false;
			if (!isOwnTask) {
				dragValidationStatus.value = 'invalid'; // 他人任务不能拖入需求池，显示红色高亮
			} else {
				dragValidationStatus.value = 'valid'; // 自己的任务可以拖入需求池，显示绿色高亮
			}
			if (currentDragData.value?.isExpired) {
				dragValidationStatus.value = 'invalid';
			}
		} else {
			dragValidationStatus.value = '';
		}
		return;
	}

	// 早期检测：如果是自己拖拽到自己，直接设置为空状态，不显示任何框框
	if (currentDragData.value?.isFromTable) {
		const sourcePersonId = currentDragData.value.sourcePersonId;
		const sourceDay = currentDragData.value.sourceDay;
		const sourcePeriod = currentDragData.value.sourcePeriod;

		// 获取目标时间槽
		const targetSlot = getTargetSlot(currentDragData.value.timeUnit, period, row, day);

		if (row.personId === sourcePersonId && sourceDay === day && sourcePeriod === targetSlot) {
			// 自己拖拽到自己，不显示任何高亮
			dragHighlightTarget.value = '';
			dragValidationStatus.value = '';
			fullDayHighlightTarget.value = '';
			return;
		}
	}

	const targetKey = `${row.personId}-${day}-${period}`;
	dragHighlightTarget.value = targetKey;

	// 检查是否需要全天高亮
	// 1. 当拖拽6h全天任务到上午或下午时
	// 2. 当已有全天任务且拖拽任何非晚上任务时（显示冲突）
	const shouldShowFullDayHighlight =
		// 拖拽6h全天任务到上午或下午
		(currentDragData.value?.timeUnit === '6' && (period === 'morning' || period === 'afternoon')) ||
		// 已有全天任务且拖拽非晚上任务时显示冲突（移除timeUnit限制）
		(row[day]?.['fullday']?.id && period !== 'evening');

	if (shouldShowFullDayHighlight) {
		const fullDayKey = `${row.personId}-${day}-fullday`;
		fullDayHighlightTarget.value = fullDayKey;
		// 确保普通高亮不与全天高亮冲突
		if (currentDragData.value?.timeUnit === '6' && (period === 'morning' || period === 'afternoon')) {
			dragHighlightTarget.value = '';
		}
	} else {
		fullDayHighlightTarget.value = '';
	}

	if (currentDragData.value) {
		// 立即进行完整验证，不使用防抖
		const timeUnit = currentDragData.value.timeUnit;
		const basicValid = validateDropTarget(timeUnit, period);

		if (!basicValid) {
			dragValidationStatus.value = 'invalid';
		} else {
			const isValid = validateDragToTarget(currentDragData.value, row, day, period);
			dragValidationStatus.value = isValid ? 'valid' : 'invalid';
		}
	} else {
		dragValidationStatus.value = '';
	}
};

// 拖拽离开区域取消高亮
const handleDragLeave = (event: DragEvent) => {
	// 使用延迟清理策略，避免快速移动时的状态闪烁
	if (validationTimer) {
		clearTimeout(validationTimer);
	}

	// 延迟清理高亮状态，给dragenter事件足够时间处理
	validationTimer = setTimeout(() => {
		// 再次检查鼠标是否确实离开了目标区域
		const currentTarget = event.currentTarget as Element;
		const relatedTarget = event.relatedTarget as Element;

		// 如果鼠标移动到了其他有效的拖拽目标，不清除状态
		if (
			relatedTarget &&
			(relatedTarget.classList.contains('time-period') ||
				relatedTarget.classList.contains('afternoon-slot') ||
				relatedTarget.classList.contains('demand-pool-drop-zone') ||
				relatedTarget.classList.contains('full-day-merged-container') ||
				relatedTarget.closest('.time-period') ||
				relatedTarget.closest('.afternoon-slot') ||
				relatedTarget.closest('.demand-pool-drop-zone') ||
				relatedTarget.closest('.full-day-merged-container'))
		) {
			return;
		}

		// 确实离开了拖拽区域，清除高亮状态
		if (!relatedTarget || !currentTarget?.contains(relatedTarget)) {
			dragHighlightTarget.value = '';
			dragValidationStatus.value = '';
			fullDayHighlightTarget.value = '';
			classCache.clear();
		}
	}, 5); // 减少延迟时间，提高响应性
};

// 检查是否为高亮目标
const isDragHighlight = (row: any, day: string, period: string) => {
	// 处理需求池等特殊区域（row为null的情况）
	if (!row) {
		const targetKey = `${day}-${period}`;
		return dragHighlightTarget.value === targetKey;
	}

	const targetKey = `${row.personId}-${day}-${period}`;
	return dragHighlightTarget.value === targetKey;
};

// 缓存类名计算结果，避免重复计算（已移除缓存逻辑以确保实时性）
const classCache = new Map<string, any>();

// 获取拖拽高亮的类名对象 - 移除缓存确保实时性
const getDragHighlightClasses = (row: any, day: string, period: string) => {
	const isHighlight = isDragHighlight(row, day, period);
	const isFullDayHighlight = isFullDayDragHighlight(row, day, period);

	let result = {};

	if (isHighlight || isFullDayHighlight) {
		const isValid = dragValidationStatus.value === 'valid';
		const isInvalid = dragValidationStatus.value === 'invalid';

		if (isFullDayHighlight) {
			result = {
				'drag-highlight-fullday-valid': isValid,
				'drag-highlight-fullday-invalid': isInvalid,
			};
		} else {
			result = {
				'drag-highlight-valid': isValid,
				'drag-highlight-invalid': isInvalid,
			};
		}
	}

	return result;
};

// 检查是否为全天任务拖拽高亮目标
const isFullDayDragHighlight = (row: any, day: string, period: string) => {
	// 检查是否设置了全天高亮目标
	if (!row || !fullDayHighlightTarget.value) {
		return false;
	}

	const fullDayKey = `${row.personId}-${day}-fullday`;
	const isTargetFullDay = fullDayHighlightTarget.value === fullDayKey;

	// 如果是全天高亮目标，则上午和下午都应该高亮
	// 这包括：1. 拖拽6h全天任务；2. 已有全天任务的冲突高亮
	return isTargetFullDay && (period === 'morning' || period === 'afternoon');
};

// 验证拖拽到目标位置是否有效
const validateDragToTarget = (draggedItem: any, row: any, day: string, period: string) => {
	if (!draggedItem || !row) return false;

	const timeUnit = draggedItem.timeUnit;
	const dateObj = props.weekDatesData.find((item) => item.key === day);
	const workDate = dateObj?.fullDateStr;

	// 检查日期是否过期
	if (workDate && workDate < new Date().toISOString().split('T')[0]) {
		return false;
	}

	// 检查时间段是否可以接受拖拽
	if (!isSlotDroppable(row, day, period)) {
		return false;
	}

	// 验证拖拽的有效性
	if (!validateDropTarget(timeUnit, period)) {
		return false;
	}

	// 确保目标位置存在
	if (!row[day]) {
		row[day] = {};
	}

	// 根据时长类型确定存储位置
	const targetSlot = getTargetSlot(timeUnit, period, row, day);

	// 检查目标位置是否已被占用
	if (isTargetSlotOccupied(row, day, targetSlot, timeUnit)) {
		return false;
	}

	// 处理从表格内移动的任务 - 避免自己拖拽到自己
	if (draggedItem.isFromTable) {
		const sourcePersonId = draggedItem.sourcePersonId;
		const sourceDay = draggedItem.sourceDay;
		const sourcePeriod = draggedItem.sourcePeriod;

		if (row.personId === sourcePersonId && sourceDay === day && sourcePeriod === targetSlot) {
			return false;
		}
	}

	return true;
};

// 任务表格拖拽后处理数据
const handleDrop = (event: DragEvent, row: any, day: string, period: string) => {
	event.preventDefault();
	
	// 隐藏删除区域 - 拖拽到任务表格时
	const hideDeleteZone = () => {
		isDraggingForDelete.value = false;
		isDragOverDelete.value = false;
		deleteZoneVisible.value = false;
		currentDeleteItem.value = null;
	};
	// 清理计时器和高亮状态
	if (validationTimer) {
		clearTimeout(validationTimer);
		validationTimer = null;
	}
	dragHighlightTarget.value = '';
	dragValidationStatus.value = '';
	fullDayHighlightTarget.value = '';
	currentDragData.value = null;
	const data = event.dataTransfer?.getData('text/plain');

	if (data) {
		const draggedItem = JSON.parse(data);
		const timeUnit = draggedItem.timeUnit;
		const dateObj = props.weekDatesData.find((item) => item.key === day);
		const workDate = dateObj?.fullDateStr;
		// 检查时间段是否可以接受拖拽
		if (!isSlotDroppable(row, day, period)) {
			// 该时间段不可接受拖拽（休假或其它任务占用或当前时段已过时效期）
			ElMessage.warning('该时间段不可接受任务安排（休假或其它任务占用或当前时段已过时效期）');
			hideDeleteZone();
			return;
		}

		// 验证拖拽的有效性
		if (!validateDropTarget(timeUnit, period)) {
			hideDeleteZone();
			return;
		}

		// 确保目标位置存在
		if (!row[day]) {
			row[day] = {};
		}

		// 根据时长类型确定存储位置
		const targetSlot = getTargetSlot(timeUnit, period, row, day);

		// 处理从表格内移动的任务 - 早期检测自己拖拽到自己
		if (draggedItem.isFromTable) {
			const sourcePersonId = draggedItem.sourcePersonId;
			const sourceDay = draggedItem.sourceDay;
			const sourcePeriod = draggedItem.sourcePeriod;

			// 检测自己拖拽到自己 - 在其他检查之前
			if (row.personId === sourcePersonId && sourceDay === day && sourcePeriod === targetSlot) {
				ElMessage.warning('任务已在当前位置');
				hideDeleteZone();
				return;
			}
		}

		// 检查目标位置是否已被占用，以及下午时间段的冲突检查
		if (isTargetSlotOccupied(row, day, targetSlot, timeUnit)) {
			// 为全天任务提供特殊的错误提示
			ElMessage.warning('该时间段不可接受任务安排（休假或其它任务占用或当前时段已过时效期）');
			// if (timeUnit === '6') {
			// 	// ElMessage.warning('已超出全天正常工作时长6h，请先清空该日期的其他任务');
			// }
			hideDeleteZone();
			return;
		}

		// 继续处理从表格内移动的任务
		if (draggedItem.isFromTable) {
			const sourcePersonId = draggedItem.sourcePersonId;
			const sourceDay = draggedItem.sourceDay;
			const sourcePeriod = draggedItem.sourcePeriod;

			// 创建新任务（保留所有字段）
			const newTask = {
				id: draggedItem.id,
				type: draggedItem.type,
				duration: draggedItem.duration,
				timeUnit: timeUnit,
				description: draggedItem.description,
				status: draggedItem.status || 1,
				isFeedback: draggedItem.isFeedback || false,
				createdByCurrentUser: draggedItem.createdByCurrentUser || false,
				isExpired: draggedItem.isExpired || false,
				workDate: draggedItem.workDate || new Date().toISOString().split('T')[0],
				assignedBy: draggedItem.assignedBy || 'unknown',
				assignedByName:''
			};

			row[day][targetSlot] = newTask;
			const params = {
					date: workDate,
					executor: row.personId,
					time_slot: targetSlot,
				};
			// 根据复制模式决定是否清除源位置的任务
			if (!taskCopyMode.value) {
				// 移动模式：清除源位置的任务
				const sourceRow = props.tableData.find((r) => r.personId === sourcePersonId);
				if (sourceRow && sourceRow[sourceDay] && sourceRow[sourceDay][sourcePeriod]) {
					delete sourceRow[sourceDay][sourcePeriod];
				}
				// 调用接口
				demandPoolWorkMoveIntoTable(draggedItem.id, params)
					.then((res) => {
						if (res.code === 200) {
							ElMessage.success('任务安排成功');
							emit('update-table-data');
						} else {
							ElMessage.error(res.message || '任务安排失败,请检查任务概览页面当前时间是否已安排工作!');
							emit('update-table-data');
						}
					})
					.catch(() => {
						ElMessage.error('任务安排失败,请检查!');
						emit('update-table-data');
					});
			} else {
				copyDemandPool(draggedItem.id, params)
					.then((res) => {
						if (res.code === 200) {
							// 如果接口返回了新任务的ID，更新本地任务的ID
							if (res.data && res.data.id && res.data.id !== draggedItem.id) {
								row[day][targetSlot].id = res.data.id;
								// 更新其他可能返回的字段
								if (res.data.description) {
									row[day][targetSlot].description = res.data.description;
								}
								if (res.data.timeUnit) {
									row[day][targetSlot].timeUnit = res.data.timeUnit;
								}
								if(res.data.create_by_name){
									row[day][targetSlot].assignedByName = res.data.create_by_name;
								}
							}
							ElMessage.success('任务安排成功');
							emit('update-table-data');
						} else {
							ElMessage.error('任务安排失败,请检查!');
							emit('update-table-data');
						}
					})
					.catch(() => {
						ElMessage.error('任务安排失败,请检查!');
						emit('update-table-data');
					});
			}
		} else {
			// 处理从需求池拖拽的任务（保留所有字段）
			const newTask = {
				id: draggedItem.id,
				type: draggedItem.type,
				duration: getDurationFromTimeUnit(timeUnit),
				timeUnit: timeUnit,
				description: draggedItem.description || `任务${draggedItem.id}`,
				category: draggedItem.type,
				status: draggedItem.status || 1,
				isFeedback: draggedItem.isFeedback || false,
				createdByCurrentUser: draggedItem.createdByCurrentUser || false,
				isExpired: draggedItem.isExpired || false,
				workDate: new Date().toISOString().split('T')[0], // 设置为当前日期
				assignedBy: 'current_user', // 当前用户分配
				assignedByName:''
			};
			//表格本地先更新
			row[day][targetSlot] = newTask;
			const params = {
				date: workDate,
				executor: row.personId,
				time_slot: targetSlot,
			};
			demandPoolWorkMoveIntoTable(draggedItem.id, params)
				.then((res) => {
					if (res.code === 200) {
						// 如果接口返回了新任务的ID，更新本地任务的ID
						if (res.data && res.data.id && res.data.id !== draggedItem.id) {
							row[day][targetSlot].id = res.data.id;
							// 更新其他可能返回的字段
							if (res.data.description) {
								row[day][targetSlot].description = res.data.description;
							}
							// if (res.data.timeUnit) {
							// 	row[day][targetSlot].timeUnit = res.data.timeUnit;
							// }
							if(res.data.create_by_name){
								row[day][targetSlot].assignedByName = res.data.create_by_name;
							}
						}
						ElMessage.success('任务安排成功');
						emit('update-table-data');
						emit('update-personal-demands', null);
					} else {
						// ElMessage.error('任务安排失败,请检查!');
						// ElMessage.error(res.msg + '请检查任务概览页面当前时间是否已安排工作!'||'任务安排失败,请检查任务概览页面当前时间是否已安排工作!');
						ElMessage.error(res.message + '请检查任务概览页面当前时间是否已安排工作!' || '任务安排失败,请检查任务概览页面当前时间是否已安排工作!');
						emit('update-table-data');
						emit('update-personal-demands', null);
					}
				})
				.catch(() => {
					// console.log('res',res);
					ElMessage.error('任务安排失败,请检查!');
					emit('update-table-data');
					emit('update-personal-demands', null);
				});
			// 从需求池中移除
			const updatedDemands = props.personalDemands.filter((d) => d.id !== draggedItem.id);
			emit('update-personal-demands', updatedDemands);
		}
	}
	
	// 无论是否处理数据都要隐藏删除区域
	hideDeleteZone();
};

// 检查目标位置是否被占用（包括下午时间段的冲突检查）
const isTargetSlotOccupied = (row: any, day: string, targetSlot: string, timeUnit: string) => {
	// 检查目标位置是否已被占用
	if (row[day][targetSlot]?.id) {
		return true;
	}

	// 全天任务的特殊冲突检查
	if (timeUnit === '6') {
		// 全天任务与任何其他时间段任务冲突|| row[day]['evening']?.id
		if (row[day]['morning']?.id || row[day]['afternoon_full']?.id || row[day]['afternoon_1']?.id || row[day]['afternoon_2']?.id) {
			return true;
		}
	} else {
		// 其他任务与全天任务的冲突检查 - 只有非晚上时间段的任务才会与全天任务冲突
		if (row[day]['fullday']?.id && targetSlot !== 'evening') {
			return true;
		}
	}

	// 下午时间段的特殊冲突检查
	if (timeUnit === '4') {
		// 下午4小时任务与分层任务冲突 - 只有当目标位置是下午时才检查冲突
		if (targetSlot === 'afternoon_full' && (row[day]['afternoon_1']?.id || row[day]['afternoon_2']?.id)) {
			return true;
		}
	} else if (timeUnit === '2') {
		// 下午2小时任务与4小时任务冲突 - 检查目标位置是否为下午分层时间段
		if ((targetSlot === 'afternoon_1' || targetSlot === 'afternoon_2') && row[day]['afternoon_full']?.id) {
			return true;
		}
	}
	//检查目标位置日期是否过期
	const dateObj = props.weekDatesData.find((item) => item.key === day);
	const workDate = dateObj?.fullDateStr;
	if (workDate < new Date().toISOString().split('T')[0]) {
		// ElMessage.warning('该日期已过任务安排时效,无法拖入!');
		return true;
	}

	return false;
};

// 验证拖拽目标是否有效
const validateDropTarget = (timeUnit: string, period: string) => {
	switch (timeUnit) {
		case '2': // 2h - 可以拖进上午或下午上半段或下午下半段
			return period === 'morning' || period === 'afternoon' || period === 'afternoon_1' || period === 'afternoon_2' || period === 'fullday';
		case '4': // 4h - 可以拖进下午全时段或者晚上加班时段
			return period === 'afternoon' || period === 'evening' || period === 'fullday';
		case '6': // 6h - 可以拖进上午或者下午，跟原来的全天(6h)同步
			return period === 'morning' || period === 'afternoon' || period === 'fullday';
		default:
			return false;
	}
};

// 根据时长类型获取目标存储位置
const getTargetSlot = (timeUnit: string, period: string, row?: any, day?: string) => {
	switch (timeUnit) {
		case '2': // 2h - 可以拖进上午或下午上半段或下午下半段
			if (period === 'morning') {
				return 'morning';
			}
			if (period === 'fullday') {
				// 拖拽到全天区域时，表示冲突，返回fullday让后续处理检测到冲突
				return 'fullday';
			}
			// 如果用户直接拖拽到具体的下午时间段，直接使用该时间段
			if (period === 'afternoon_1' || period === 'afternoon_2') {
				return period;
			}
			// 否则进行智能分配：优先选择空闲的时间段
			if (row && day) {
				// 检查14:00-16:00是否空闲
				if (!row[day]?.['afternoon_1']) {
					return 'afternoon_1';
				}
				// 检查16:00-18:00是否空闲
				if (!row[day]?.['afternoon_2']) {
					return 'afternoon_2';
				}
			}
			// 默认返回第一个时间段
			return 'afternoon_1';
		case '4': // 4h - 可以拖进下午全时段或者晚上加班时段
			if (period === 'evening') {
				return 'evening';
			}
			if (period === 'fullday') {
				// 拖拽到全天区域时，表示冲突，返回fullday让后续处理检测到冲突
				return 'fullday';
			}
			return 'afternoon_full';
		case '6': // 6h - 可以拖进上午或者下午，跟原来的全天(6h)同步
			return 'fullday';
		default:
			return period;
	}
};

const getDurationFromTimeUnit = (timeUnit: string) => {
	switch (timeUnit) {
		case '2':
			return 2;
		case '4':
			return 4;
		case '6':
			return 6;
		default:
			return 2;
	}
};

const handleTaskContextMenu = (event: MouseEvent, row: any, day: string, period: string) => {
	// 先清理所有已存在的菜单
	clearContextMenus();
	const task = row[day][period];

	const menu = document.createElement('div');
	menu.className = 'task-context-menu';
	if(task.status === 1){
		menu.innerHTML = `
			<div class="menu-item" id="move-back">移回需求池</div>
			<div class="menu-item" id="delete-task">删除任务</div>
		`;
	}else{
		// 休假任务
		menu.innerHTML = `
			<div class="menu-item" id="delete-task">删除任务</div>		
		`;
	}
	menu.style.position = 'fixed';
	menu.style.left = `${event.clientX}px`;
	menu.style.top = `${event.clientY}px`;
	menu.style.zIndex = '1000';
	document.body.appendChild(menu);


	const handleMenuClick = (e: MouseEvent) => {
		const target = e.target as HTMLElement;
		if (target.id === 'move-back') {
			if (!task.createdByCurrentUser) {
				ElMessage.warning('不支持将其他人的任务安排移回需求池!');
				return;
			}
			if (!task.isExpired) {
				const demand = {
					type: task.type,
					timeUnit: task.timeUnit,
					id: task.id,
					description: task.description || `任务${task.id}`,
					category: task.type,
				};
				const updatedDemands = [...props.personalDemands, demand];
				emit('update-personal-demands', updatedDemands);
				delete row[day][period];
				// 调用接口
				const params = {
					date: null,
					executor: null,
					time_slot: null,
				};
				demandPoolWorkMoveIntoTable(task.id, params)
					.then((res) => {
						if (res.code === 200) {
							ElMessage.success('任务移入成功');
							emit('update-table-data');
							emit('update-personal-demands', null);
						} else {
							ElMessage.error('任务移入失败,请检查!');
							emit('update-table-data');
							emit('update-personal-demands', null);
						}
					})
					.catch(() => {
						ElMessage.error('任务移入失败,请检查!');
						emit('update-table-data');
						emit('update-personal-demands', null);
					});
			} else {
				ElMessage.warning('当前任务已过时效期,无法移回需求池');
			}
		} else if (target.id === 'delete-task') {
			delete row[day][period];
			deleteDemandPool(task.id)
				.then((res) => {
					if (res.code === 200 || res.code === 204) {
						ElMessage.success('删除任务成功');
						emit('update-table-data');
					} else {
						ElMessage.error('删除任务失败,请检查!');
						emit('update-table-data');
					}
				})
				.catch(() => {
					ElMessage.error('删除任务失败,请检查!');
					emit('update-table-data');
				});
		}

		if (menu.parentNode) {
			menu.parentNode.removeChild(menu);
		}
		document.removeEventListener('click', handleClickOutside);
	};

	const handleClickOutside = (e: MouseEvent) => {
		if (!menu.contains(e.target as Node)) {
			if (menu.parentNode) {
				menu.parentNode.removeChild(menu);
			}
			document.removeEventListener('click', handleClickOutside);
		}
	};

	menu.addEventListener('click', handleMenuClick);
	// 使用 setTimeout 确保当前右键事件处理完成后再添加 click 监听
	setTimeout(() => {
		document.addEventListener('click', handleClickOutside);
	}, 0);
};

const changeWeek = (direction: number) => {
	emit('change-week', direction);
};

// 切换需求池展开收起状态
const toggleDemandPool = () => {
	demandPoolExpanded.value = !demandPoolExpanded.value;
};
// 从表格拖拽到需求池处理数据
const handleDemandPoolDrop = (event: DragEvent) => {
	event.preventDefault();
	// 清理计时器和高亮状态
	if (validationTimer) {
		clearTimeout(validationTimer);
		validationTimer = null;
	}
	dragHighlightTarget.value = '';
	dragValidationStatus.value = '';
	fullDayHighlightTarget.value = '';
	currentDragData.value = null;
	
	// 隐藏删除区域 - 拖拽到需求池时
	const hideDeleteZone = () => {
		isDraggingForDelete.value = false;
		isDragOverDelete.value = false;
		deleteZoneVisible.value = false;
		currentDeleteItem.value = null;
	};
	
	const data = event.dataTransfer?.getData('text/plain');
	
	try {
		if (data) {
			const draggedItem = JSON.parse(data);

			if (draggedItem.isFromTable) {
				if (!draggedItem.createdByCurrentUser) {
					ElMessage.warning('不支持将其他人的任务安排移入个人需求池!');
					return;
				}
				if (draggedItem.isExpired) {
					ElMessage.warning('过期任务无法移入个人需求池!');
					return;
				}

				const newDemand = {
					type: draggedItem.type,
					timeUnit: draggedItem.timeUnit || getTimeUnitFromTask(draggedItem),
					id: draggedItem.id,
					description: draggedItem.description || `任务${draggedItem.id}`,
					category: draggedItem.type,
				};

				const updatedDemands = [...props.personalDemands, newDemand];
				emit('update-personal-demands', updatedDemands);
				//本地更新表格
				const sourcePersonId = draggedItem.sourcePersonId;
				const sourceDay = draggedItem.sourceDay;
				const sourcePeriod = draggedItem.sourcePeriod;

				// 根据复制模式决定是否清除源位置的任务
				// if (!taskCopyMode.value) {
				// 	// 移动模式：清除源位置的任务
				// 	const sourceRow = props.tableData.find((r) => r.personId === sourcePersonId);
				// 	if (sourceRow && (sourceRow as any)[sourceDay] && (sourceRow as any)[sourceDay][sourcePeriod]) {
				// 		delete (sourceRow as any)[sourceDay][sourcePeriod];
				// 	}
				// }
				const sourceRow = props.tableData.find((r) => r.personId === sourcePersonId);
					if (sourceRow && (sourceRow as any)[sourceDay] && (sourceRow as any)[sourceDay][sourcePeriod]) {
						delete (sourceRow as any)[sourceDay][sourcePeriod];
					}
				// 调用接口
				const params = {
					date: null,
					executor: null,
					time_slot: null,
				};
				demandPoolWorkMoveIntoTable(draggedItem.id, params)
					.then((res) => {
						if (res.code === 200) {
							ElMessage.success('任务移入成功');
							emit('update-table-data');
							emit('update-personal-demands', null);
						} else {
							ElMessage.error('任务移入失败,请检查!');
							emit('update-table-data');
							emit('update-personal-demands', null);
						}
					})
					.catch(() => {
						ElMessage.error('任务移入失败,请检查!');
						emit('update-table-data');
						emit('update-personal-demands', null);
					});
			}
		}
	} catch (error) {
		// 处理JSON解析错误等
	} finally {
		// 无论任何情况都要隐藏删除区域
		hideDeleteZone();
	}
};

const getTimeUnitFromTask = (task: any) => {
	if (task.duration) {
		const duration = parseInt(task.duration.toString());
		if (duration === 2) return '2';
		if (duration === 4) return '4';
		if (duration === 6) return '6';
	}
	return '';
};

// const handleEditSubmit = (updatedDemands: any[]) => {
// 	emit('update-personal-demands', updatedDemands);
// 	editDialogVisible.value = false;
// };

const handleDemandCardClick = (demand: any, event: Event) => {
	// 如果正在拖拽，不触发点击编辑
	if (isDragging.value) {
		isDragging.value = false;
		return;
	}
	event.preventDefault();
	emit('edit-demand', demand);
};

// 需求池内部拖拽结束处理
const handleDemandDragEnd = () => {
	setTimeout(() => {
		isDragging.value = false;
		currentDraggedDemand.value = null;
		currentDragData.value = null;
		categoryDragHighlight.value = '';
		
		// 重置删除功能状态
		isDraggingForDelete.value = false;
		isDragOverDelete.value = false;
		deleteZoneVisible.value = false;
		currentDeleteItem.value = null;
	}, 100);
};

// 兼容原有的拖拽结束方法
const handleDragEnd = () => {
	// 清理计时器
	if (validationTimer) {
		clearTimeout(validationTimer);
		validationTimer = null;
	}

	// 延迟重置拖拽状态，避免拖拽结束时立即触发点击
	setTimeout(() => {
		isDragging.value = false;
		currentDragData.value = null;
		dragHighlightTarget.value = '';
		dragValidationStatus.value = '';
		fullDayHighlightTarget.value = '';
		
		// 重置删除功能状态
		isDraggingForDelete.value = false;
		isDragOverDelete.value = false;
		deleteZoneVisible.value = false;
		currentDeleteItem.value = null;
	}, 100);
};

const copyMode = ref(false);
const deleteMode = ref(false);

const handleCopyModeChange = () => {
	// 如果开启复制模式，关闭删除模式
	if (copyMode.value) {
		deleteMode.value = false;
	}
};

const handleDeleteModeChange = () => {
	// 如果开启删除模式，关闭复制模式
	if (deleteMode.value) {
		copyMode.value = false;
	}
};

const handleCopyDemand = async (demand: any) => {
	try {
		// 调用复用需求接口
		const response = await copyDemandPool(demand.id);
		if (response.code === 200) {
			// const copiedDemand = {
			// 	...demand,
			// };
			// 添加到需求池
			// const updatedDemands = [...props.personalDemands, copiedDemand];
			// emit('update-personal-demands', updatedDemands);
			emit('update-personal-demands', null);
			ElMessage.success('复用需求成功');
		}
	} catch (error) {
		ElMessage.error('复用需求失败，请稍后重试');
	}
};

const handleDeleteDemand = async (id: number) => {
	try {
		// 调用删除接口
		await deleteDemandPool(id);

		// 删除成功后从本地需求池中移除
		// const updatedDemands = props.personalDemands.filter((demand) => demand.id !== id);
		// emit('update-personal-demands', updatedDemands);
		emit('update-personal-demands', null);

		ElMessage.success('删除需求成功');
	} catch (error) {
		ElMessage.error('删除需求失败，请稍后重试');
	}
};

// 处理下午时间槽的drop事件，特殊处理4小时任务和6小时全天任务
const handleAfternoonSlotDrop = (event: DragEvent, row: any, day: string, period: string) => {
	// 隐藏删除区域 - 拖拽到下午时间槽时
	const hideDeleteZone = () => {
		isDraggingForDelete.value = false;
		isDragOverDelete.value = false;
		deleteZoneVisible.value = false;
		currentDeleteItem.value = null;
	};
	
	const data = event.dataTransfer?.getData('text/plain');
	if (data) {
		const draggedItem = JSON.parse(data);
		// 如果是4小时任务或6小时全天任务，不阻止冒泡，让外层处理
		if (draggedItem.timeUnit === '4' || draggedItem.timeUnit === '6') {
			hideDeleteZone();
			return;
		}
	}
	// 2小时任务正常处理并阻止冒泡
	handleDrop(event, row, day, period);
	event.stopPropagation();
	hideDeleteZone();
};

// 类别拖拽相关方法
const getCategoryDropZoneClass = () => {
	return {
		'category-drop-zone': true
	};
};

const getCategoryHighlightStyle = (categoryValue: string) => {
	// 获取需求池内容区域的DOM元素
	const demandContentArea = document.querySelector('.demand-content-area') as HTMLElement;
	if (!demandContentArea) return {};
	
	// 获取内容区域在视口中的位置
	const contentRect = demandContentArea.getBoundingClientRect();
	
	// 根据类别计算列的位置
	const categoryIndex = demandCategories.findIndex(c => c.value === categoryValue);
	const totalCategories = demandCategories.length;
	const columnWidth = contentRect.width / totalCategories;
	const columnLeft = contentRect.left + (categoryIndex * columnWidth);
	
	return {
		position: 'fixed' as const,
		left: `${columnLeft}px`,
		top: `${contentRect.top}px`,
		width: `${columnWidth}px`,
		height: `${contentRect.height}px`,
		zIndex: 1000
	};
};

const handleCategoryDragEnter = (event: DragEvent, categoryValue: string) => {
	// 只有当拖拽的是需求池内部的需求时才高亮
	if (currentDraggedDemand.value && currentDraggedDemand.value.category !== categoryValue) {
		categoryDragHighlight.value = categoryValue;
	}
};

const handleCategoryDragLeave = (event: DragEvent) => {
	// 检查是否真的离开了类别区域
	const target = event.target as HTMLElement;
	const relatedTarget = event.relatedTarget as HTMLElement;
	
	if (!relatedTarget || !target.contains(relatedTarget)) {
		categoryDragHighlight.value = '';
	}
};

const handleCategoryDrop = async (event: DragEvent, targetCategory: string) => {
	event.preventDefault();
	categoryDragHighlight.value = '';
	
	// 隐藏删除区域 - 拖拽到类别区域时
	const hideDeleteZone = () => {
		isDraggingForDelete.value = false;
		isDragOverDelete.value = false;
		deleteZoneVisible.value = false;
		currentDeleteItem.value = null;
	};
	
	const data = event.dataTransfer?.getData('text/plain');
	if (!data) {
		hideDeleteZone();
		return;
	}
	
	try {
		const draggedItem = JSON.parse(data);
		
		// 只处理需求池内部拖拽
		if (!draggedItem.isFromDemandPool) {
			hideDeleteZone();
			return;
		}
		
		// 如果拖拽到相同类别，不做处理
		if (draggedItem.category === targetCategory) {
			ElMessage.info('需求已在当前类别中');
			hideDeleteZone();
			return;
		}
		
		// 调用API更新需求类型
		const { updateDemandPool } = useDemandPoolApi();
		const updateData = {
			...draggedItem,
			needy_type: targetCategory,
		};
		
		const response = await updateDemandPool(draggedItem.id, updateData);
		
		if (response.code === 200) {
			ElMessage.success(`需求已移动到${demandCategories.find(c => c.value === targetCategory)?.label}类别`);
			// 刷新需求池数据
			emit('update-personal-demands', null);
		} else {
			ElMessage.error('需求类型更新失败');
		}
	} catch (error) {
		ElMessage.error('拖拽操作失败');
	} finally {
		// 无论成功还是失败都要隐藏删除区域
		hideDeleteZone();
	}
};

// 删除区域事件处理方法
const handleDeleteDragEnter = (event: DragEvent) => {
	event.preventDefault();
	if (isDraggingForDelete.value && canDelete.value) {
		isDragOverDelete.value = true;
	}
};

const handleDeleteDragLeave = (event: DragEvent) => {
	// 检查是否真的离开了删除区域
	const target = event.target as HTMLElement;
	const relatedTarget = event.relatedTarget as HTMLElement;
	
	if (!relatedTarget || !target.contains(relatedTarget)) {
		isDragOverDelete.value = false;
	}
};

const handleDeleteDrop = async (event: DragEvent) => {
	event.preventDefault();
	isDragOverDelete.value = false;
	
	// 确保删除区域消失 - 不管是否执行删除都要隐藏
	const hideDeleteZone = () => {
		isDraggingForDelete.value = false;
		isDragOverDelete.value = false;
		deleteZoneVisible.value = false;
		currentDeleteItem.value = null;
	};
	
	if (!currentDeleteItem.value || !canDelete.value) {
		ElMessage.warning('该需求已过时效期，无法删除');
		hideDeleteZone();
		return;
	}
	
	try {
		if (currentDeleteItem.value.isFromDemandPool) {
			// 删除需求池项目
			await deleteDemandPool(currentDeleteItem.value.id);
			ElMessage.success('需求删除成功');
			emit('update-personal-demands', null);
		} else if (currentDeleteItem.value.isFromTable) {
			// 删除任务表格项目 - 这里可以调用具体的任务删除API
			await deleteDemandPool(currentDeleteItem.value.id);
			ElMessage.success('任务删除成功');
			emit('update-table-data');
		}
	} catch (error) {
		ElMessage.error('删除失败，请稍后重试');
	} finally {
		// 无论成功还是失败都要隐藏删除区域
		hideDeleteZone();
	}
};

const getPeriodClass = (period: string) => {
	if (period === 'morning') return 'morning-period';
	if (period === 'afternoon') return 'afternoon-period';
	if (period === 'evening') return 'evening-period';
	return '';
};

const getFullDayTask = (row: any, day: string) => {
	return row[day]?.['fullday'];
};

const getMorningTask = (row: any, day: string) => {
	return row[day]?.['morning'];
};

const getAfternoonFullTask = (row: any, day: string) => {
	return row[day]?.['afternoon_full'];
};

const getAfternoonTask1 = (row: any, day: string) => {
	return row[day]?.['afternoon_1'];
};

const getAfternoonTask2 = (row: any, day: string) => {
	return row[day]?.['afternoon_2'];
};

const getEveningTask = (row: any, day: string) => {
	return row[day]?.['evening'];
};

const hasTaskInPeriod = (row: any, day: string, period: string) => {
	if (!row[day]) return false;

	// 检查当前时间段是否有任务
	if (period === 'morning') {
		return !!(row[day]['morning'] || row[day]['fullday']);
	} else if (period === 'afternoon') {
		// 下午时间段总是返回true，因为下午有分层显示，不需要显示"下午"标签
		return true;
	} else if (period === 'evening') {
		return !!row[day]['evening'];
	}

	return false;
};

// 获取任务卡片的样式类
const getTaskCardClass = (task: any) => {
	// 安全检查：如果task不存在，返回默认class
	if (!task) {
		return 'task-item';
	}

	const classes = ['task-item'];

	if (task.status === 2) {
		classes.push('vacation-task');
	}

	if (task.isFeedback) {
		classes.push('feedback-task');
	}

	// 根据"只看自己"开关和任务创建者判断是否置灰
	if (onlyShowSelf.value && !task.createdByCurrentUser) {
		classes.push('other-user-task');
	}

	return classes.join(' ');
};

// 获取任务显示的描述
const getTaskDisplayDescription = (task: any) => {
	if (task.status === 2) {
		return '休假';
	}
	return task.description;
};

// 检查任务是否可以拖拽
const isTaskDraggable = (task: any) => {
	// 原定&实际模式下，原定安排不允许拖拽
	if (displayMode.value === 'both') {
		return false;
	}

	// 休假状态不能拖拽
	if (task.status === 2) {
		return false;
	}

	// 过期需求不能拖拽
	// if (task.isExpired) {
	// 	return false;
	// }

	// 开启"只看自己"模式时，非自己创建的任务不能拖拽
	if (onlyShowSelf.value && !task.createdByCurrentUser) {
		return false;
	}

	return true;
};

// 检查时间段是否可以接受拖拽
const isSlotDroppable = (row: any, day: string, period: string) => {
	// 检查当前时间段是否已有休假安排
	const existingTask = getTaskInPeriod(row, day, period);
	if (existingTask && existingTask.status === 2) {
		return false;
	}

	return true;
};

// 获取指定时间段的任务
const getTaskInPeriod = (row: any, day: string, period: string) => {
	if (!row[day]) return null;

	switch (period) {
		case 'morning':
			return row[day]['morning'] || row[day]['fullday'];
		case 'afternoon':
			return row[day]['afternoon_full'] || row[day]['afternoon_1'] || row[day]['afternoon_2'] || row[day]['fullday'];
		case 'afternoon_1':
			return row[day]['afternoon_1'];
		case 'afternoon_2':
			return row[day]['afternoon_2'];
		case 'evening':
			return row[day]['evening'];
		case 'fullday':
			return row[day]['fullday'];
		default:
			return null;
	}
};

// 获取任务卡片背景色
const getTaskCardBgColor = (task: any) => {
	// 休假状态返回灰色
	if (task.status === 2) {
		return '#f5f5f5';
	}

	// 已反馈任务返回绿色背景
	if (task.isFeedback) {
		return '#c3ead5';
	}

	// 开启"只看自己"模式时，非自己创建的任务置灰
	if (onlyShowSelf.value && !task.createdByCurrentUser) {
		return '#f5f5f5';
	}

	// 原定安排的任务统一使用 #ffe9e8 背景色
	return '#ffe9e8';
};

// 获取实际执行任务的方法
const getActualFullDayTask = (row: any, day: string) => {
	return row[`${day}_actual`]?.['fullday'];
};

const getActualMorningTask = (row: any, day: string) => {
	return row[`${day}_actual`]?.['morning'];
};

const getActualAfternoonFullTask = (row: any, day: string) => {
	return row[`${day}_actual`]?.['afternoon_full'];
};

const getActualAfternoonTask1 = (row: any, day: string) => {
	return row[`${day}_actual`]?.['afternoon_1'];
};

const getActualAfternoonTask2 = (row: any, day: string) => {
	return row[`${day}_actual`]?.['afternoon_2'];
};

const getActualEveningTask = (row: any, day: string) => {
	return row[`${day}_actual`]?.['evening'];
};

// 检查实际执行任务是否存在的方法
const hasActualTaskInPeriod = (row: any, day: string, period: string) => {
	if (!row[`${day}_actual`]) return false;

	// 检查当前时间段是否有实际任务
	if (period === 'morning') {
		return !!(row[`${day}_actual`]['morning'] || row[`${day}_actual`]['fullday']);
	} else if (period === 'afternoon') {
		// 下午时间段检查是否有任何下午相关的实际任务
		return !!(
			row[`${day}_actual`]['afternoon_full'] ||
			row[`${day}_actual`]['afternoon_1'] ||
			row[`${day}_actual`]['afternoon_2'] ||
			row[`${day}_actual`]['fullday']
		);
	} else if (period === 'evening') {
		return !!row[`${day}_actual`]['evening'];
	}

	return false;
};

const getActualTaskCardClass = (task: any) => {
	// 安全检查：如果task不存在，返回默认class
	if (!task) {
		return 'task-item';
	}

	const classes = ['task-item'];

	if (task.status === 2) {
		classes.push('vacation-task');
	}

	if (task.isFeedback) {
		classes.push('feedback-task');
	}

	// 根据"只看自己"开关和任务创建者判断是否置灰
	if (onlyShowSelf.value && !task.createdByCurrentUser) {
		classes.push('other-user-task');
	}

	return classes.join(' ');
};

// 获取实际执行任务的背景色
const getActualTaskCardBgColor = (task: any) => {
	// 休假状态返回灰色
	if (task.status === 2) {
		return '#f5f5f5';
	}

	// 已反馈任务返回绿色背景
	if (task.isFeedback) {
		return '#c3ead5';
	}

	// 开启"只看自己"模式时，非自己创建的任务置灰
	if (onlyShowSelf.value && !task.createdByCurrentUser) {
		return '#f5f5f5';
	}

	// 实际执行任务统一使用 #ffeead 背景色
	return '#ffeead';
};

// 任务点击处理方法
const handleTaskClick = (task: any, row: any, day: string, period: string, workType: string = 'original') => {
	// 先清理所有已存在的右键菜单
	clearContextMenus();

	// 原定&实际模式下，如果是原定任务，给出提示但仍允许查看详情
	if (displayMode.value === 'both' && workType === 'original') {
		ElMessage.info('原定&实际模式下，原定安排为只读状态');
	}

	const dateObj = props.weekDatesData.find((item) => item.key === day);
	const workDate = dateObj?.fullDateStr;
	const taskDetail = {
		...task,
		executorName: row.person || row.personName,
		workDate: workDate,
		period: period,
		workType: workType,
	};
	emit('task-click', taskDetail);
};

// 筛选功能方法
const workTypeChange = () => {
	emit('filter-change', {
		type: 'workType',
		value: localFilterForm.value.workType,
	});
};

const businessChange = () => {
	// 清空人员选择
	// localFilterForm.value.person = [];

	// 触发业务变更事件
	emit('filter-change', {
		type: 'business',
		value: localFilterForm.value.business,
	});
};

const personChange = () => {
	// 对人员选择进行去重处理
	if (localFilterForm.value.person && localFilterForm.value.person.length > 0) {
		const uniquePersons = Array.from(new Set(localFilterForm.value.person));
		localFilterForm.value.person = uniquePersons;
	}
	
	// 触发人员变更事件
	emit('filter-change', {
		type: 'person',
		value: localFilterForm.value.person,
	});
};
</script>

<style scoped lang="scss">
// 右侧主要工作区域
.main-work-area {
	display: flex;
	flex-direction: column;
	gap: 16px;
	overflow: hidden;
	height: 100%;
	padding: 0 0 0 8px; // 左侧留出一点空间给分隔条

	.demand-pool-panel {
		flex: 0 0 auto;
		max-height: 230px;
		transition: max-height 0.3s ease;

		&.collapsed {
			max-height: 60px;
		}

		.panel-section {
			height: 100%;
			display: flex;
			flex-direction: column;
		}

		.section-content {
			flex: 1;
			overflow-y: auto;
		}

		.demand-pool-drop-zone {
			position: relative;
			min-height: 150px;
			transition: all 0.3s;

			&:hover {
				background-color: rgba(64, 158, 255, 0.05);
			}

			// 拖拽有效高亮样式
			&.drag-highlight-valid {
				background-color: rgba(103, 194, 58, 0.1) !important; // 淡绿色背景
				border: 2px dashed #67c23a !important; // 绿色虚线框
				transition: all 0.2s ease;
			}

			// 拖拽无效高亮样式
			&.drag-highlight-invalid {
				background-color: rgba(245, 108, 108, 0.1) !important; // 淡红色背景
				border: 2px dashed #f56c6c !important; // 红色虚线框
				transition: all 0.2s ease;
			}
		}

		.empty-pool {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			height: 150px;
			color: #909399;

			.empty-icon {
				font-size: 48px;
				margin-bottom: 16px;
				opacity: 0.5;
			}

			p {
				margin: 4px 0;
				font-size: 14px;

				&.empty-tip {
					font-size: 12px;
					opacity: 0.7;
				}
			}
		}

		.demand-pool-container {
			display: flex;
			flex-direction: column;
			height: 100%;

			.category-headers-row {
				display: grid;
				grid-template-columns: repeat(6, 1fr);
				gap: 12px;
				// padding: 0 0 8px 0;
				padding-right: 8px;
				flex-shrink: 0;

				.category-header-cell {
					font-size: 13px;
					font-weight: 600;
					color: #303133;
					padding: 6px 10px;
					background: linear-gradient(135deg, #f5f7fa 0%, #e8eaed 100%);
					border-radius: 6px;
					text-align: center;
					border: 1px solid #e4e7ed;
					box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
				}
			}

			.demand-content-area {
				flex: 1;
				overflow-y: auto;
				padding-right: 2px;
				padding-top: 8px;

				&::-webkit-scrollbar {
					width: 4px;
				}

				&::-webkit-scrollbar-track {
					background: #f1f1f1;
					border-radius: 2px;
				}

				&::-webkit-scrollbar-thumb {
					background: #c1c1c1;
					border-radius: 2px;

					&:hover {
						background: #a8a8a8;
					}
				}

				.demand-categories-grid {
					display: grid;
					grid-template-columns: repeat(6, 1fr);
					gap: 12px;
					min-height: 100%;
					overflow: hidden; // 防止网格溢出

					.category-column {
						display: flex;
						flex-direction: column;
						min-width: 0; // 允许列收缩
						max-width: 100%; // 限制列最大宽度
						overflow: hidden; // 防止列溢出

						.demand-items-container {
							display: flex;
							flex-direction: column;
							gap: 6px;
							min-width: 0; // 允许容器收缩
							max-width: 100%; // 限制容器最大宽度
						}

						.demand-card {
							position: relative;
							padding: 6px 8px;
							border: 1px solid rgba(0, 0, 0, 0.1);
							border-radius: 6px;
							cursor: pointer;
							transition: all 0.2s ease;
							background: white;
							box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
							min-height: 41px;
							display: flex;
							align-items: center;
							flex-shrink: 0;
							width: 100%; // 确保占满列宽
							max-width: 100%; // 防止内容撑开
							box-sizing: border-box; // 包含padding和border
							overflow: hidden; // 防止内容溢出

							&:hover {
								box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
								transform: translateY(-2px);
							}

							&:active {
								transform: scale(0.98) translateY(-1px);
								opacity: 0.9;
							}

							.demand-content {
								flex: 1;
								display: flex;
								align-items: center;
								gap: 8px;
								min-width: 0; // 关键：允许flex子元素收缩
								width: 100%;
								max-width: 100%; // 防止超出父容器
								overflow: hidden; // 防止内容溢出

								.demand-time-badge {
									font-size: 11px;
									font-weight: 600;
									color: #409eff;
									flex-shrink: 0;
									white-space: nowrap;
									max-width: 70px; // 限制时间标签最大宽度
									overflow: hidden;
									text-overflow: ellipsis;

									// 多行显示样式
									&.multi-line {
										white-space: normal;
										max-width: 80px; // 多行时稍微增加宽度
										line-height: 1.2;
										display: flex;
										flex-direction: column;
										gap: 2px; // 减少间距

										.time-line {
											font-size: 10px;
											line-height: 1.1;
											white-space: nowrap;
											overflow: hidden;
											text-overflow: ellipsis;
											max-width: 100%;
										}

										.time-divider {
											height: 1px;
											border-bottom: 1px dashed #909399;
											//margin: 1px 10% 1px 10%; // 左右留10%的空白，让虚线居中且不占满宽度
										}
									}
								}

								.demand-description {
									font-size: 11px;
									color: #606266;
									line-height: 1.3;
									flex: 1;
									min-width: 0; // 关键：允许文本容器收缩
									overflow: hidden;
									text-overflow: ellipsis;
									white-space: nowrap;
									max-width: 100%; // 确保不超过容器宽度
								}
							}

							.action-button {
								position: absolute;
								top: 2px;
								right: 2px;
								width: 18px;
								height: 18px;
								border-radius: 50%;
								display: flex;
								align-items: center;
								justify-content: center;
								font-size: 12px;
								font-weight: bold;
								cursor: pointer;
								transition: all 0.2s ease;
								z-index: 10;
								box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);

								&.copy-button {
									background: #67c23a;
									color: white;
									font-size: 10px;
									&:hover {
										background: #85ce61;
										transform: scale(1.1);
										box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
									}
								}

								&.delete-button {
									background: #f56c6c;
									color: white;
									font-size: 14px;
									line-height: 1;

									&:hover {
										background: #f78989;
										transform: scale(1.1);
										box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
									}
								}
							}
						}
					}
				}
			}
		}
	}

	.arrangement-panel {
		flex: 1;
		overflow: hidden;

		.panel-section {
			height: 100%;
			display: flex;
			flex-direction: column;
		}

		.section-content {
			flex: 1;
			overflow: hidden;
			display: flex;
			flex-direction: column;
		}

		.arrangement-controls {
			display: flex;
			align-items: center;
			gap: 16px;
			margin-left: auto;

			.week-nav {
				display: flex;
				gap: 0;
				border-radius: 6px;
				overflow: hidden;
				box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

				.week-nav-button {
					position: relative;
					width: 60px;
					height: 32px;
					cursor: pointer;
					transition: all 0.2s ease;
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 14px;
					font-weight: 500;
					user-select: none;
					border: none;

					.week-nav-text {
						position: relative;
						z-index: 2;
						color: #fff;
						text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
					}

					&.prev-week {
						background: #409eff;
						border-radius: 6px 0 0 6px;

						&:hover {
							background: #66b1ff;
						}

						&:active {
							background: #337ecc;
						}
					}

					&.current-week {
						background: #67c23a;

						&:hover {
							background: #85ce61;
						}

						&:active {
							background: #529b2e;
						}
					}

					&.next-week {
						background: #409eff;
						border-radius: 0 6px 6px 0;

						&:hover {
							background: #66b1ff;
						}

						&:active {
							background: #337ecc;
						}
					}
				}
			}
		}

		.task-table {
			flex: 1; // 占用剩余空间
			height: 0; // 强制依赖flex布局
			overflow: auto;
			//padding-bottom: 35px;

			// both模式下确保有足够的最小宽度
			min-width: 100%;

			.task-cell {
				display: flex;
				flex-direction: column;
				gap: 1px;
				background-color: #f8f9fa;
			}
		}

		.display-mode-controls {
			display: flex;
			margin-bottom: 10px;
			flex-shrink: 0; // 防止被压缩
			align-items: center;
			gap: 16px;
			justify-content: space-between;
			.display-mode-controls-left {
				display: flex;
				align-items: center;
				gap: 10px;
			}

					.task-copy-switch,
		.self-only-switch {
			display: flex;
			align-items: center;

			:deep(.el-switch) {
				.el-switch__label {
					font-size: 12px;
					font-weight: 500;
					margin-left: 8px;
				}

				&.is-checked {
					.el-switch__core {
						border-color: #409eff;
						background-color: #409eff;
					}
				}
			}
		}

			.filter-controls {
				display: flex;
				align-items: center;
				gap: 12px;
				margin-left: 16px;
				.filter-item {
					display: flex;
					align-items: center;
					gap: 6px;

					.filter-label {
						font-size: 12px;
						color: #606266;
						font-weight: 500;
						white-space: nowrap;
					}

					:deep(.el-select) {
						.el-input__wrapper {
							font-size: 12px;
						}
					}
				}
			}
		}
	}
}

.panel-section {
	background: #fff;
	border-radius: 8px;
	padding: 20px;
	padding-bottom: 10px;
	padding-top: 10px;
	box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
	border: 1px solid #ebeef5;

	.section-title {
		font-size: 16px;
		font-weight: 600;
		padding-bottom: 12px;
		display: flex;
		align-items: center;
		justify-content: space-between;

		.title-left {
			display: flex;
			align-items: center;
			gap: 4px;

			.expand-icon {
				cursor: pointer;
				display: flex;
				align-items: center;
				justify-content: center;
				width: 24px;
				height: 24px;
				border-radius: 4px;
				transition: all 0.2s ease;

				&:hover {
					background-color: #f5f7fa;
					color: #409eff;
				}

				:deep(.el-icon) {
					font-size: 16px;
					// color: #606266;
					transition: color 0.2s ease;
				}

				&:hover :deep(.el-icon) {
					color: #409eff;
				}
			}
		}

		.week-range {
			font-size: 14px;
			font-weight: 400;
			color: #606266;
			margin-left: 8px;
		}

		.copy-mode-tip {
			font-size: 14px;
			font-weight: 500;
			color: #e6a23c;
			margin-left: 16px;
			display: flex;
			align-items: center;
			gap: 4px;
			
			:deep(.el-icon) {
				font-size: 16px;
			}
		}

		.pool-controls {
			display: flex;
			align-items: center;
			gap: 16px;

			.control-item {
				display: flex;
				align-items: center;

				:deep(.el-switch) {
					.el-switch__label {
						font-size: 12px;
						font-weight: 500;
						margin-left: 8px;
					}

					&.is-checked {
						.el-switch__core {
							border-color: #409eff;
							background-color: #409eff;
						}
					}
				}
			}
		}
	}

	.section-content {
		.el-form-item {
			margin-bottom: 20px;
		}
	}
}

// 表格样式优化
:deep(.el-table) {
	.el-table__header {
		th {
			background-color: #fafafa;
			color: #606266;
			font-weight: 500;

			// 今日列的表头样式
			&.today-column {
				background-color: #e3f2fd !important;
			}
		}
	}

	.el-table__body {
		tr {
			td:nth-child(n + 3) {
				padding: 0;

				.cell {
					padding: 0;
				}
			}

			// 今日列的表格内容样式
			td.today-column {
				background-color: #eaf5fe !important;

				.task-cell {
					background-color: transparent !important;
					.time-period {
						background-color: transparent !important;
						.tasks-container {
							background-color: transparent !important;
						}
					}
					.full-day-merged-container {
						background-color: transparent !important;
					}
				}
			}
		}
	}

	// 表头固定
	.el-table__header-wrapper {
		position: sticky;
		top: 0;
		z-index: 10;
	}
}

// 日期表头样式
.day-header {
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 14px;
	font-weight: 600;
	color: #303133;
	width: 100%;
	height: 100%;

	// 今日表头高亮样式
	&.today-header {
		color: #1976d2;
		font-weight: 700;
		position: relative;
	}
}

// 统一任务卡片样式 - 与需求池保持一致
.task-item {
	position: relative;
	padding: 6px 8px;
	border: 1px solid rgba(0, 0, 0, 0.1);
	border-radius: 6px;
	cursor: pointer;
	transition: all 0.2s ease;
	background: white;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
	display: flex;
	align-items: center;
	flex-shrink: 0;
	//min-height: 32px; // 设置固定的最小高度而非height: 100%
	max-height: 100%; // 限制最大高度不超过容器
	width: 100%; // 确保占满容器宽度
	box-sizing: border-box; // 包含padding和border在内

	&:hover {
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
		transform: translateY(-2px);
	}

	&:active {
		transform: scale(0.98) translateY(-1px);
		opacity: 0.9;
	}

	// 休假状态样式
	&.vacation-task {
		background: #f5f5f5 !important; // 置灰背景
		cursor: not-allowed;

		&:hover {
			transform: none;
			box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
		}
	}

	// 已反馈任务样式
	&.feedback-task {
		background: #c3ead5 !important; // 绿色背景
		border: 1px solid #a5d6a7;

		&:hover {
			box-shadow: 0 4px 12px rgba(165, 214, 167, 0.4);
			transform: translateY(-2px);
		}
	}

	// 非自己创建的任务样式
	&.other-user-task {
		background: #f5f5f5 !important; // 置灰背景
		opacity: 0.6; // 降低透明度
		cursor: not-allowed;

		&:hover {
			transform: none;
			box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
		}

		.demand-content {
			.demand-time-badge,
			.demand-description {
				color: #c0c4cc !important; // 文字颜色置灰
			}
		}
	}

	// 实际任务样式
	&.actual-task {
		background: #ffeead;
	}

	.demand-content {
		flex: 1;
		display: flex;
		align-items: center;
		gap: 8px;
		min-width: 0; // 关键：允许flex子元素收缩
		width: 100%;

		&.small {
			gap: 4px;
		}

		.demand-time-badge {
			font-size: 11px;
			font-weight: 600;
			color: #409eff;
			flex-shrink: 0;
			white-space: nowrap;

			&.small {
				font-size: 10px;
			}
		}

		.demand-description {
			font-size: 11px;
			color: #606266;
			line-height: 1.3;
			flex: 1;
			min-width: 0; // 关键：允许文本容器收缩
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			max-width: 100%; // 确保不超过容器宽度

			&.small {
				font-size: 10px;
				line-height: 1.1;
			}
		}
	}
}

// 时间段样式
.time-period {
	background-color: #fff;
	position: relative;
	transition: background-color 0.3s;
	border-bottom: 1px solid #f0f0f0;
	display: flex;
	flex-direction: column;

	// 默认时间段高度
	flex: 1;
	min-height: 65px; // 对应下午格子高度

	// 上午时间段：60%高度
	&.morning-period {
		flex: 0.6;
		min-height: 39px; // 65 * 0.6 = 39
	}

	// 下午时间段：标准高度
	&.afternoon-period {
		flex: 1;
		min-height: 65px;
	}

	// 晚上时间段：60%高度
	&.evening-period {
		flex: 0.6;
		min-height: 39px; // 65 * 0.6 = 39
	}

	// 双卡片模式的特殊处理
	&.both-mode {
		.tasks-container.both-mode {
			padding: 3px; // 双卡片模式下减少padding
		}
	}

	// 全天任务拖拽高亮 - 使用固定定位确保边框完整显示
	&.morning-period {
		&.drag-highlight-fullday-valid,
		&.drag-highlight-fullday-invalid {
			position: relative;
			z-index: 10;

			&::before {
				content: '';
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				border: 2px dashed transparent;
				border-bottom: none; // 上午格子不显示底边，避免中间重复虚线
				border-radius: 4px 4px 0 0; // 只有顶部圆角
				z-index: 1;
				pointer-events: none;
			}

			&::after {
				content: '';
				position: absolute;
				top: 100%;
				left: 0;
				right: 0;
				height: 167%;
				border: 2px dashed transparent;
				border-top: none; // 下午格子不显示顶边，避免中间重复虚线
				border-radius: 0 0 4px 4px; // 只有底部圆角
				z-index: 1;
				pointer-events: none;
			}
		}

		&.drag-highlight-fullday-valid {
			&::before,
			&::after {
				border-color: #67c23a;
				background-color: rgba(103, 194, 58, 0.1);
			}
		}

		&.drag-highlight-fullday-invalid {
			&::before,
			&::after {
				border-color: #f56c6c;
				background-color: rgba(245, 108, 108, 0.1);
			}
		}
	}

	// 下午格子在全天高亮时完全隐藏高亮
	&.afternoon-period {
		&.drag-highlight-fullday-valid,
		&.drag-highlight-fullday-invalid {
			// 不显示任何高亮效果，由上午格子的伪元素覆盖
		}
	}

	&:hover {
		background-color: #f5f7fa;
	}

	&.drag-over {
		background-color: #e3f2fd;
		border: 2px dashed #409eff;
	}

	&.drag-invalid {
		background-color: #fef0f0;
		border: 2px dashed #f56c6c;
	}

	// 拖拽高亮样式
	&.drag-highlight {
		background-color: rgba(103, 194, 58, 0.1) !important; // 淡绿色背景
		border: 2px dashed #67c23a !important; // 绿色虚线框
		transition: all 0.2s ease;
	}

	// 拖拽有效高亮样式
	&.drag-highlight-valid {
		background-color: rgba(103, 194, 58, 0.1) !important; // 淡绿色背景
		border: 2px dashed #67c23a !important; // 绿色虚线框
		transition: all 0.2s ease;
	}

	// 拖拽无效高亮样式
	&.drag-highlight-invalid {
		background-color: rgba(245, 108, 108, 0.1) !important; // 淡红色背景
		border: 2px dashed #f56c6c !important; // 红色虚线框
		transition: all 0.2s ease;
	}

	.period-label {
		position: absolute;
		top: 2px;
		left: 4px;
		font-size: 12px;
		color: #c0c4cc;
		z-index: 2;
	}

	// both模式下上午时间段的period-label居中样式
	&.morning-period .period-label {
		// position: absolute;
		// top: 50%;
		// left: 50%;
		// transform: translate(-50%, -50%);
		// font-size: 12px;
		// color: #c0c4cc;
		// z-index: 1;
		// pointer-events: none;
		// white-space: nowrap;
	}

	.tasks-container {
		flex: 1;
		padding: 6px;
		position: relative;
		min-width: 0; // 允许容器收缩
		width: 100%; // 确保占满父容器

		// 双卡片模式容器
		&.both-mode {
			.task-cards-wrapper {
				display: flex;
				flex-direction: column;
				gap: 2px;
				height: 100%;
				width: 100%; // 确保占满宽度
				min-width: 0; // 允许收缩

				&.small {
					gap: 1px;
				}

				.task-item {
					flex: 1;
					margin-bottom: 0;
					min-height: 0;
					width: 100%; // 确保占满宽度
				}
			}
		}

		.afternoon-tasks-area {
			height: 100%;

			&.both-mode {
				.afternoon-split-area.both-mode {
					.afternoon-slot.both-mode {
						.task-cards-wrapper {
							display: flex;
							flex-direction: column;
							gap: 1px;
							height: 100%;

							.task-item {
								flex: 1;
								margin: 0;
								height: auto;
								min-height: 0;
							}
						}
					}
				}
			}

			.afternoon-split-area {
				display: flex;
				flex-direction: column;
				gap: 1px;
				height: 100%;
				margin: -2px -2px 2px -2px;

				&.both-mode {
					.afternoon-slot.both-mode {
						padding: 1px;

						&.has-task {
							padding: 0;
						}
					}
				}

				.afternoon-slot {
					flex: 1;
					position: relative;
					border: 1px dashed #e0e0e0;
					border-radius: 4px;
					background: rgba(248, 249, 250, 0.5);
					transition: all 0.2s ease;

					&:hover {
						border-color: #409eff;
						background: rgba(64, 158, 255, 0.05);
					}

					// 拖拽高亮样式
					&.drag-highlight {
						background-color: rgba(103, 194, 58, 0.1) !important; // 淡绿色背景
						border: 2px dashed #67c23a !important; // 绿色虚线框
						transition: all 0.2s ease;
					}

					// 拖拽有效高亮样式
					&.drag-highlight-valid {
						background-color: rgba(103, 194, 58, 0.1) !important; // 淡绿色背景
						border: 2px dashed #67c23a !important; // 绿色虚线框
						transition: all 0.2s ease;
					}

					// 拖拽无效高亮样式
					&.drag-highlight-invalid {
						background-color: rgba(245, 108, 108, 0.1) !important; // 淡红色背景
						border: 2px dashed #f56c6c !important; // 红色虚线框
						transition: all 0.2s ease;
					}

					.time-slot-label {
						position: absolute;
						top: 50%;
						left: 50%;
						transform: translate(-50%, -50%);
						// font-size: 8px;
						font-size: 12px;
						color: #c0c4cc;
						z-index: 1;
						pointer-events: none;
						white-space: nowrap;
					}

					&.slot-14-16,
					&.slot-16-18 {
						padding: 1px;
						display: flex;
						align-items: center;
						justify-content: center;

						&.has-task {
							padding: 0;
						}
					}

					.small-task {
						width: 100%;
						height: 100%;
						min-height: unset;
						max-height: 100%;
						margin: 0;
						border-radius: 2px;
						padding: 1px 3px;
						font-size: 9px;
						box-sizing: border-box; // 确保padding计算正确

						&:hover {
							transform: none;
							box-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
						}

						&:active {
							transform: scale(0.98);
						}

						.demand-content.small {
							gap: 1px;
							min-width: 0; // 确保可以收缩
							width: 100%;

							.demand-time-badge.small {
								font-size: 11px;
								flex-shrink: 0; // 时间标签不收缩
							}

							.demand-description.small {
								font-size: 11px;
								line-height: 1.1;
								min-width: 0; // 关键：允许文本收缩
								overflow: hidden;
								text-overflow: ellipsis;
								white-space: nowrap;
							}
						}
					}
				}
			}
		}

		// 下午时间段的特殊处理
		&.afternoon-period .tasks-container {
			padding: 3px;
		}
	}
}

// 双卡片模式下的容器样式
.task-cards-wrapper {
	display: flex;
	flex-direction: column;
	gap: 2px;
	height: 100%;
	width: 100%; // 确保占满宽度
	min-width: 0; // 允许收缩

	&.small {
		gap: 1px;
	}

	.task-item {
		flex: 1;
		margin-bottom: 0;
		min-height: 0;
		width: 100%; // 确保占满宽度
	}
}

// 全天任务合并容器
.full-day-merged-container {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 8px;
	background: #fff;
	position: relative;
	flex: 1.6;
	transition: all 0.2s ease;

	// 拖拽高亮样式
	&.drag-highlight-valid {
		background-color: rgba(103, 194, 58, 0.1) !important;
		border: 2px dashed #67c23a !important;
		border-radius: 4px;
	}

	&.drag-highlight-invalid {
		background-color: rgba(245, 108, 108, 0.1) !important;
		border: 2px dashed #f56c6c !important;
		border-radius: 4px;
	}

	&.both-mode {
		flex-direction: column;
		gap: 2px;
		padding: 4px;

		.full-day-merged-task {
			width: 100%;
			flex: 1;
			min-height: 26px;
		}
	}

	.full-day-merged-task {
		width: 100%;
		min-height: 55px;
		z-index: 2;
	}
}

.both-mode-container {
	display: flex;
	height: 100%;
	border: 1px solid #e4e7ed;
	border-radius: 4px;
	overflow: hidden;
	min-width: 300px; // 确保两列都有足够的显示空间 (320px - padding/border)

	.original-column,
	.actual-column {
		flex: 1;
		min-width: 130px; // 每列最小宽度缩小1/3
		display: flex;
		flex-direction: column;
		height: 100%;

		.column-header {
			background-color: #f5f7fa;
			padding: 6px 12px; // 增加padding
			font-size: 12px; // 稍微增大字体
			font-weight: 500;
			color: #606266;
			text-align: center;
			border-bottom: 1px solid #e4e7ed;
			line-height: 1;
			flex-shrink: 0; // 防止header被压缩
		}

		.column-content {
			flex: 1;
			overflow: hidden;
			position: relative;
			display: flex;
			flex-direction: column;

			// 完全复用外部的时间段样式
			.time-period {
				// 继承外部的所有time-period样式
				background-color: #fff;
				position: relative;
				transition: background-color 0.3s;
				border-bottom: 1px solid #f0f0f0;
				display: flex;
				flex-direction: column;

				// 与原定模式完全一致的高度分配
				&.morning-period {
					flex: 0.6;
					min-height: 39px;
				}

				&.afternoon-period {
					flex: 1;
					min-height: 65px;
				}

				&.evening-period {
					flex: 0.6;
					min-height: 39px;
				}

				&:hover {
					background-color: #f5f7fa;
				}

				&.drag-over {
					background-color: #e3f2fd;
					border: 2px dashed #409eff;
				}

				&.drag-invalid {
					background-color: #fef0f0;
					border: 2px dashed #f56c6c;
				}

				.period-label {
					position: absolute;
					top: 2px;
					left: 4px;
					font-size: 12px;
					color: #c0c4cc;
					z-index: 2;
				}

				// both模式下上午时间段的period-label居中样式
				&.morning-period .period-label {
					// position: absolute;
					// top: 50%;
					// left: 50%;
					// transform: translate(-50%, -50%);
					// //font-size: 8px;
					// font-size: 12px;
					// color: #c0c4cc;
					// z-index: 1;
					// pointer-events: none;
					// white-space: nowrap;
				}

				// both模式下的全天任务拖拽高亮处理 - 使用双伪元素确保边框完整
				&.morning-period {
					&.drag-highlight-fullday-valid,
					&.drag-highlight-fullday-invalid {
						position: relative;
						z-index: 10;

						&::before {
							content: '';
							position: absolute;
							top: 0;
							left: 0;
							right: 0;
							bottom: 0;
							border: 2px dashed transparent;
							border-bottom: none; // 上午格子不显示底边，避免中间重复虚线
							border-radius: 4px 4px 0 0; // 只有顶部圆角
							z-index: 1;
							pointer-events: none;
						}

						&::after {
							content: '';
							position: absolute;
							top: 100%;
							left: 0;
							right: 0;
							height: 167%;
							border: 2px dashed transparent;
							border-top: none; // 下午格子不显示顶边，避免中间重复虚线
							border-radius: 0 0 4px 4px; // 只有底部圆角
							z-index: 1;
							pointer-events: none;
						}
					}

					&.drag-highlight-fullday-valid {
						&::before,
						&::after {
							border-color: #67c23a;
							background-color: rgba(103, 194, 58, 0.1);
						}
					}

					&.drag-highlight-fullday-invalid {
						&::before,
						&::after {
							border-color: #f56c6c;
							background-color: rgba(245, 108, 108, 0.1);
						}
					}
				}

				// both模式下午格子在全天高亮时隐藏高亮
				&.afternoon-period {
					&.drag-highlight-fullday-valid,
					&.drag-highlight-fullday-invalid {
						// 不显示任何高亮效果，由上午格子的伪元素覆盖
					}
				}

				.tasks-container {
					flex: 1;
					padding: 6px;
					position: relative;

					.task-item {
						// 移除多余的margin调整，使用原始样式
						&:not(.small-task) {
							margin-bottom: 0;
						}
					}
				}
			}

			// 全天任务合并容器
			.full-day-merged-container {
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 8px;
				background: #fff;
				position: relative;
				// 与原定模式一致的flex分配：上午(0.6) + 下午(1) = 1.6
				flex: 1.6;

				.full-day-merged-task {
					width: 100%;
					min-height: 55px;
					z-index: 2;
				}
			}

			// 下午任务区域
			.afternoon-tasks-area {
				height: 100%;

				.afternoon-split-area {
					display: flex;
					flex-direction: column;
					gap: 1px;
					height: 100%;
					margin: -2px;

					.afternoon-slot {
						flex: 1;
						position: relative;
						border: 1px dashed #e0e0e0;
						border-radius: 4px;
						background: rgba(248, 249, 250, 0.5);
						padding: 1px;
						transition: all 0.2s ease;
						display: flex;
						align-items: center;
						justify-content: center;

						&:hover {
							border-color: #409eff;
							background: rgba(64, 158, 255, 0.05);
						}

						&.has-task {
							padding: 0;
						}

						.time-slot-label {
							position: absolute;
							top: 50%;
							left: 50%;
							transform: translate(-50%, -50%);
							// font-size: 8px;
							font-size: 12px;
							color: #c0c4cc;
							z-index: 1;
							pointer-events: none;
							white-space: nowrap;
						}

						.small-task {
							width: 100%;
							height: 100%;
							margin: 0;
							border-radius: 2px;
							padding: 1px 3px;
							font-size: 9px;

							&:hover {
								transform: none;
								box-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
							}

							&:active {
								transform: scale(0.98);
							}

							.demand-content.small {
								gap: 1px;

								.demand-time-badge.small {
									font-size: 11px;
								}

								.demand-description.small {
									font-size: 11px;
									line-height: 1.1;
								}
							}
						}
					}
				}
			}
		}
	}

	.original-column {
		border-right: 1px solid #e4e7ed;

		.column-header {
			color: #409eff;
		}

		// 原定列中的任务卡片样式调整
		.column-content {
			.task-item {
				cursor: default !important;
				//opacity: 0.8;

				&:hover {
					transform: none !important;
					box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
				}

				&:active {
					transform: none !important;
					//opacity: 0.8 !important;
				}
			}

			// 时间段的拖拽提示
			.time-period {
				&:hover {
					background-color: #fff !important;
				}
			}
		}
	}

	.actual-column {
		.column-header {
			color: #67c23a;
		}

		// 实际任务的统一背景色
		.task-item.actual-task {
			background-color: #ffeead;
		}
	}
}

// 实际任务样式
.actual-task {
	background-color: #ffeead;
	// border: 1px solid #f0c862;

	&:hover {
		box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
		transform: none !important;
	}

	&:active {
		transform: none !important;
		opacity: 1 !important;
	}
}

// 需求池类别拖拽样式
.demand-content-area {
	position: relative;
}

.category-highlight-overlay {
	// position将通过style动态设置为fixed
	background-color: rgba(64, 158, 255, 0.1);
	border: 3px dashed #409eff;
	border-radius: 8px;
	pointer-events: none;
	display: flex;
	align-items: center;
	justify-content: center;
	animation: dragHighlightPulse 1.5s infinite;
	
	.drag-hint-text {
		// background: rgba(64, 158, 255, 0.95);
		color: white;
		padding: 10px 16px;
		border-radius: 6px;
		font-size: 12px;
		font-weight: 400;
		white-space: nowrap;
		// box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
		// animation: messageFloat 2s ease-in-out infinite;
	}
}

@keyframes dragHighlightPulse {
	0%, 100% {
		border-color: #409eff;
		background-color: rgba(64, 158, 255, 0.1);
	}
	50% {
		border-color: #66b1ff;
		background-color: rgba(64, 158, 255, 0.15);
	}
}

@keyframes messageFloat {
	0%, 100% {
		transform: translateY(0);
	}
	50% {
		transform: translateY(-3px);
	}
}

.category-column {
	transition: all 0.2s ease;
	
	&.category-drop-zone {
		position: relative;
	}
}

// 删除区域样式
.delete-zone {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	height: 70px;
	background: linear-gradient(135deg, rgba(245, 108, 108, 0.05) 0%, rgba(245, 108, 108, 0.1) 100%);
	border-bottom: 2px dashed rgba(245, 108, 108, 0.3);
	z-index: 1001;
	display: flex;
	align-items: center;
	justify-content: center;
	transform: translateY(-100%);
	transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
	backdrop-filter: blur(10px);
	
	&.delete-zone-visible {
		transform: translateY(0);
	}
	
	&.delete-zone-active {
		border-bottom-color: rgba(245, 108, 108, 0.6);
		background: linear-gradient(135deg, rgba(245, 108, 108, 0.08) 0%, rgba(245, 108, 108, 0.15) 100%);
	}
	
	&.delete-zone-hover {
		transform: translateY(0) scale(1.02);
		background: linear-gradient(135deg, rgba(245, 108, 108, 0.15) 0%, rgba(245, 108, 108, 0.25) 100%);
		border-bottom-color: #f56c6c;
		box-shadow: 0 4px 20px rgba(245, 108, 108, 0.3);
		animation: deleteZonePulse 1.5s infinite;
		
		.delete-icon {
			transform: scale(1.2);
			animation: deleteIconShake 0.6s infinite;
		}
		
		.delete-warning {
			animation: deleteTextFloat 2s ease-in-out infinite;
		}
	}
	
	.delete-content {
		display: flex;
		align-items: center;
		gap: 12px;
		padding: 16px 24px;
		border-radius: 12px;
		background: rgba(255, 255, 255, 0.9);
		box-shadow: 0 4px 12px rgba(245, 108, 108, 0.2);
		backdrop-filter: blur(20px);
		transition: all 0.3s ease;
	}
	
	.delete-icon {
		font-size: 24px;
		color: #f56c6c;
		transition: all 0.3s ease;
	}
	
	.delete-text, .delete-warning {
		font-size: 14px;
		font-weight: 500;
		color: #f56c6c;
		white-space: nowrap;
		transition: all 0.3s ease;
	}
	
	.delete-warning {
		font-weight: 600;
		color: #e6363e;
	}
}

@keyframes deleteZonePulse {
	0%, 100% {
		box-shadow: 0 4px 20px rgba(245, 108, 108, 0.3);
	}
	50% {
		box-shadow: 0 6px 30px rgba(245, 108, 108, 0.5);
	}
}

@keyframes deleteIconShake {
	0%, 100% { transform: scale(1.2) rotate(0deg); }
	25% { transform: scale(1.2) rotate(-3deg); }
	75% { transform: scale(1.2) rotate(3deg); }
}

@keyframes deleteTextFloat {
	0%, 100% { transform: translateY(0); }
	50% { transform: translateY(-2px); }
}

// 拖拽引导提示样式
.drag-guide-tip {
	position: fixed;
	bottom: 0px;
	left: 50%;
	transform: translateX(-50%);
	z-index: 1000;
	pointer-events: none;
	animation: guideSlideUp 0.5s ease-out;
	
	.guide-content {
		display: flex;
		align-items: center;
		gap: 8px;
		padding: 12px 20px;
		background: rgba(64, 158, 255, 0.95);
		color: white;
		border-radius: 25px;
		box-shadow: 0 6px 20px rgba(64, 158, 255, 0.3);
		backdrop-filter: blur(10px);
		font-size: 14px;
		font-weight: 500;
		white-space: nowrap;
		animation: guidePulse 2s ease-in-out infinite;
	}
	
	.guide-icon {
		font-size: 18px;
		animation: guideArrowBounce 1.5s ease-in-out infinite;
	}
	
	.guide-text {
		user-select: none;
	}
}

@keyframes guideSlideUp {
	from {
		opacity: 0;
		transform: translateX(-50%) translateY(30px);
	}
	to {
		opacity: 1;
		transform: translateX(-50%) translateY(0);
	}
}

@keyframes guidePulse {
	0%, 100% {
		box-shadow: 0 6px 20px rgba(64, 158, 255, 0.3);
	}
	50% {
		box-shadow: 0 8px 30px rgba(64, 158, 255, 0.5);
	}
}

@keyframes guideArrowBounce {
	0%, 100% {
		transform: translateY(0);
	}
	50% {
		transform: translateY(-4px);
	}
}
</style>

<!-- 下拉菜单样式 -->
<!-- <style>
.el-dropdown-menu {
	min-width: 60px;
	padding: 4px 0;

	.el-dropdown-menu__item {
		font-size: 12px;
		padding: 6px 12px;
		line-height: 1.2;

		&:hover {
			background-color: #f5f7fa;
		}

		&[aria-label*='删除']:hover {
			background-color: #fef0f0;
			color: #f56c6c;
		}
	}
}
</style> -->
