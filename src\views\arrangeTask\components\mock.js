export const mockData = {
    "success": true,
    "message": "获取周任务安排成功",
    "data": [
        {
            // 人员信息 用于显示任务安排表人员 业务等信息
            "person_info": {
                "person_id": "P001", // 人员id
                "person_name": "张三", // 人员姓名
                "business_group": "business1", // 业务组
                "feature_group": "feature1", // 功能组
                "current_user_id": "U001" // 当前用户id
            },
            "week_start_date": "2024-01-15", // 周开始日期
            "week_end_date": "2024-01-21", // 周结束日期
            // 每日任务安排
            "daily_tasks": [
                {
                    "work_date": "2024-01-15", // 工作日期
                    "day_of_week": "monday", // 星期几
                    // 所有时间段 里面 6种时间段都有 
                    "time_slots": [
                        {
                            "time_slot": "morning", // 具体时间段
                            // morning: 上午时段 (09:00-11:00)
                            // afternoon_1: 下午前段 (14:00-16:00)
                            // afternoon_2: 下午后段 (16:00-18:00)
                            // afternoon_full: 下午全段 (14:00-18:00)
                            // fullday: 全天 (09:00-18:00)
                            // evening: 晚上加班 (19:00-23:00)

                            // 计划任务
                            "planned_task": {
                                "task_id": 1001, // 任务id
                                "task_type": "perf", // 任务类型
                                "description": "首页加载性能优化", // 任务描述
                                "duration": 2, // 任务时长
                                "time_unit": "2", // 时间单位标识 ("2", "2-afternoon", "4", "6", "4-overtime")
                                "status": 1, // 任务状态 1:正常 2:休假
                                "is_feedback": true, // 是否反馈
                                "created_by_current_user": true, // 是否当前用户创建
                                "is_expired": false, // 是否过期
                                "work_date": "2024-01-15", // 任务日期
                                "assigned_by": "U001", // 任务指派人
                                "created_at": "2024-01-10T09:00:00Z", // 任务创建时间
                                "updated_at": "2024-01-10T09:00:00Z" // 任务更新时间
                            },
                            // 实际任务
                            "actual_task": {
                                "task_id": 1002, // 任务id
                                "task_type": "perf", // 任务类型
                                "description": "实际执行：性能监控配置", // 任务描述
                                "duration": 2, // 任务时长
                                "time_unit": "2", // 任务单位
                                "status": 1, // 任务状态 1:正常 2:休假
                                "is_feedback": false, // 是否反馈
                                "created_by_current_user": true, // 是否当前用户创建
                                "is_expired": false, // 是否过期
                                "work_date": "2024-01-15", // 任务日期
                                "assigned_by": "U001", // 任务指派人
                                "created_at": "2024-01-15T09:00:00Z", // 任务创建时间
                                "updated_at": "2024-01-15T11:00:00Z" // 任务更新时间
                            }
                        },
                        {
                            "time_slot": "afternoon_1",
                            "planned_task": {
                                "task_id": 1003,
                                "task_type": "agree",
                                "description": "第三方支付API对接",
                                "duration": 2,
                                "time_unit": "2-afternoon",
                                "status": 1,
                                "is_feedback": false,
                                "created_by_current_user": false,
                                "is_expired": false,
                                "work_date": "2024-01-15",
                                "assigned_by": "U002",
                                "created_at": "2024-01-12T14:00:00Z",
                                "updated_at": "2024-01-12T14:00:00Z"
                            },
                            "actual_task": null
                        },
                        {
                            "time_slot": "afternoon_2",
                            "planned_task": null,
                            "actual_task": null 
                        },
                        {
                            "time_slot": "afternoon_full",
                            "planned_task": null,
                            "actual_task": {
                                "task_id": 1004,
                                "task_type": "dev",
                                "description": "实际执行：紧急BUG修复",
                                "duration": 4,
                                "time_unit": "4",
                                "status": 1,
                                "is_feedback": false,
                                "created_by_current_user": true,
                                "is_expired": false,
                                "work_date": "2024-01-15",
                                "assigned_by": "U001",
                                "created_at": "2024-01-15T14:00:00Z",
                                "updated_at": "2024-01-15T18:00:00Z"
                            }
                        },
                        {
                            "time_slot": "fullday",
                            "planned_task": null,
                            "actual_task": null
                        },
                        {
                            "time_slot": "evening",
                            "planned_task": null,
                            "actual_task": null
                        }
                    ]
                },
                // 其他6天的数据结构相同
            ]
        }
        // ... 其他人员的数据结构相同
    ],

}