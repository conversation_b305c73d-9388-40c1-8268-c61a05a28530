<template>
	<div class="arrange-task-container">
		<div class="arrange-task-padding layout-padding-auto layout-padding-view">
			<div class="panel-container">
				<!-- 左侧功能面板 -->
				<div class="left-panel" :class="{ 'is-collapsed': isCollapsed }">
					<div class="collapse-button" @click="toggleCollapse">
						<el-icon>
							<ArrowLeft v-if="!isCollapsed" />
							<ArrowRight v-else />
						</el-icon>
					</div>
					<div class="panel-content" :class="{ 'is-collapsed': isCollapsed }">
						<FunctionPanel
							ref="functionPanelRef"
							:personal-demands="personalDemands"
							:selected-task="selectedTask"
							:loading="loading"
							@create-demand="handleCreateDemand"
							@update-demand="handleUpdateDemand"
							@refresh-demand-pool="refreshDemandPool"
						/>
					</div>
				</div>

				<!-- 右侧工作区域 -->
				<div class="right-panel">
					<WorkArea
						:personal-demands="personalDemands"
						:table-data="tableData"
						:filter-form="filterForm"
						:week-dates-data="weekDatesData"
						:week-date-range="weekDateRange"
						@update-table-data="handleUpdateTableData"
						@update-personal-demands="handleUpdatePersonalDemands"
						@edit-demand="handleEditDemand"
						@change-week="changeWeek"
						@task-click="handleTaskClick"
						@filter-change="handleFilterChange"
					/>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import FunctionPanel from './components/FunctionPanel.vue';
import WorkArea from './components/WorkArea.vue';
import { useDemandPoolApi } from '/@/api/demandPool';
import { ElMessage } from 'element-plus';
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue';

const { getDemandPoolList, getTableHeader, getTaskTableData } = useDemandPoolApi();
// 组件引用
const functionPanelRef = ref();

// 选中的任务详情
const selectedTask = ref(null);

// 基础数据
const weekDays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

// 当前日期（用于表头接口请求）
const currentDate = ref(new Date());

// 表头数据
const weekDatesData = ref<any[]>([]);

// 当前周的日期范围
const weekDateRange = ref({
	startDate: '',
	endDate: '',
});

// 添加收缩状态变量
const isCollapsed = ref(false);

// 获取当前日期的字符串格式（YYYY-MM-DD）
const getCurrentDateString = (date: Date) => {
	const year = date.getFullYear();
	const month = String(date.getMonth() + 1).padStart(2, '0');
	const day = String(date.getDate()).padStart(2, '0');
	return `${year}-${month}-${day}`;
};

// 获取指定日期所在周的周一日期
const getMondayOfWeek = (date: Date) => {
	const day = date.getDay();
	const diff = date.getDate() - day + (day === 0 ? -6 : 1); // 如果是周日，调整为前一周周一
	const monday = new Date(date.setDate(diff));
	return monday;
};

// 获取表头数据
const fetchTableHeader = async (date?: Date) => {
	try {
		const targetDate = date || currentDate.value;
		const dateString = getCurrentDateString(targetDate);

		const response = await getTableHeader({ date: dateString });
		if (response && response.code === 200 && response.data) {
			// 保存周期间信息
			weekDateRange.value = {
				startDate: response.start_date || '',
				endDate: response.end_date || '',
			};

			// 将接口返回的数据转换为组件需要的格式
			weekDatesData.value = response.data.map((item: any, index: number) => {
				// 从start_date中解析年份
				const startYear = new Date(response.start_date.replace(/\//g, '-')).getFullYear();
				// 构造完整日期：年份 + 月/日
				const fullDate = new Date(`${startYear}/${item.date}`);

				return {
					key: weekDays[index], // 使用现有的英文key
					date: fullDate, // 完整日期对象
					dateStr: item.date, // 使用接口返回的格式化日期 (如 "6/2")
					fullDateStr: getCurrentDateString(fullDate), // YYYY-MM-DD格式
					label: item.key, // 中文标签 (如 "周一")
				};
			});
		}
	} catch (error) {
		ElMessage.error('获取表头数据失败，请稍后重试');
		// 如果接口失败，使用默认数据
		setDefaultWeekDates();
	}
};

// 获取表格内容数据
const fetchTableData = async (date?: Date) => {
	try {
		const targetDate = date || currentDate.value;
		const dateString = getCurrentDateString(targetDate);
		const params: any = {
			date: dateString,
		};
		if (filterForm.value.person && filterForm.value.person.length > 0) {
			params.ids = [...new Set(filterForm.value.person)].join(',');
		}
		if (filterForm.value.business) {
			params.business_type = filterForm.value.business;
		}
		const response = await getTaskTableData(params);
		if (response && response.code === 200 && response.data) {
			// 转换接口数据为组件需要的格式
			tableData.value = transformTableData(response.data);
		}
	} catch (error) {
		ElMessage.error('获取表格数据失败，请稍后重试');
		// 如果接口失败，保持空数组
		tableData.value = [];
	}
};

// 转换表格数据格式
const transformTableData = (apiData: any[]) => {
	return apiData.map((userItem: any) => {
		const transformedUser: any = {
			person: userItem.real_name,
			department: userItem.company, // 暂时使用company作为department
			businessGroup: userItem.business_type_name,
			featureGroup: '', // 接口暂无此字段
			personId: userItem.id.toString(),
			currentUserId: '', // 接口暂无此字段
		};

		// 处理每一天的任务数据
		userItem.tasks.forEach((dayTask: any) => {
			const dayKey = dayTask.day_of_week; // monday, tuesday, etc.
			const actualKey = `${dayKey}_actual`;

			// 初始化该天的数据对象
			transformedUser[dayKey] = {};
			transformedUser[actualKey] = {};

			// 处理每个时间段的数据
			dayTask.time_slots.forEach((slot: any) => {
				const timeSlot = slot.time_slot;

				// 处理需求任务 (needy)
				if (slot.needy) {
					transformedUser[dayKey][timeSlot] = transformTaskItem(slot.needy, dayTask.date);
				}

				// 处理实际执行任务 (execute)
				if (slot.execute) {
					transformedUser[actualKey][timeSlot] = transformTaskItem(slot.execute, dayTask.date, true);
				}
			});
		});

		return transformedUser;
	});
};

// 转换单个任务项
const transformTaskItem = (taskItem: any, workDate: string, isActual: boolean = false) => {
	return {
		id: taskItem.id,
		type: taskItem.needy_type,
		duration: taskItem.duration,
		timeUnit: taskItem.time_map,
		description: taskItem.description,
		status: taskItem.status,
		isFeedback: taskItem.is_feedback,
		createdByCurrentUser: taskItem.created_by_current_user || false,
		isExpired: taskItem.is_expired || false,
		workDate: workDate,
		assignedBy: taskItem.leader || taskItem.create_by?.toString() || '',
		assignedByName: taskItem.leader_name || taskItem.create_by_name || '',
		executor: taskItem.executor?.toString() || '',
		isActual: isActual,
	};
};

// 设置默认表头数据（作为fallback）
const setDefaultWeekDates = () => {
	const dates = [];
	const today = new Date();
	const monday = getMondayOfWeek(new Date(today));

	// 计算周末（周日）
	const sunday = new Date(monday);
	sunday.setDate(monday.getDate() + 6);

	// 设置默认周期间
	weekDateRange.value = {
		startDate: monday.toLocaleDateString('zh-CN').replace(/\//g, '/'),
		endDate: sunday.toLocaleDateString('zh-CN').replace(/\//g, '/'),
	};

	for (let i = 0; i < 7; i++) {
		const date = new Date(monday);
		date.setDate(monday.getDate() + i);
		dates.push({
			key: weekDays[i],
			date: date,
			dateStr: date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' }), // MM/DD格式
			fullDateStr: getCurrentDateString(date), // YYYY-MM-DD格式
		});
	}
	weekDatesData.value = dates;
};

// 切换周的方法
const changeWeek = (direction: number) => {
	if (direction === 0) {
		// 重置到当前日期
		currentDate.value = new Date();
		fetchTableHeader(currentDate.value);
		fetchTableData(currentDate.value);
	} else {
		// 计算目标周的周一日期
		const currentMonday = getMondayOfWeek(new Date(currentDate.value));
		const targetMonday = new Date(currentMonday);
		targetMonday.setDate(currentMonday.getDate() + direction * 7);

		currentDate.value = targetMonday;
		fetchTableHeader(targetMonday);
		fetchTableData(targetMonday);
	}
};

// 筛选表单数据
const filterForm = ref({
	workType: 'all',
	person: [] as string[],
	business: '',
});

// 需求接口类型定义
interface DemandItem {
	id: number;
	create_by: number;
	date: string | null;
	needy_type: string;
	description: string;
	duration: number;
	time_map: string;
	time_slot: string | null;
	status: number;
	is_feedback: boolean;
	create_time: string;
	update_time: string;
	executor: string | null;
	// 前端计算字段
	type: string;
	timeUnit: string;
	category: string;
	isFeedback: boolean;
	createDate: string;
}

// 个人需求池
const personalDemands = ref<DemandItem[]>([]);

// 加载状态
const loading = ref(false);

// 表格数据
const tableData = ref<any[]>([]);

// 获取需求池数据
const fetchDemandPoolData = async () => {
	loading.value = true;
	try {
		const response = await getDemandPoolList();
		if (response && response.data) {
			// 数据转换：将后端字段映射到前端使用的字段，过滤掉 needy_type 为 "默认" 的数据
			personalDemands.value = response.data
				.filter((item: any) => item.needy_type !== '默认')
				.map((item: any) => ({
					// 保持原始字段
					...item,
					// 映射到前端使用的字段
					type: item.needy_type,
					timeUnit: item.time_map,
					category: item.needy_type,
					isFeedback: item.is_feedback,
					createDate: item.create_time,
				}));
		}
	} catch (error) {
		ElMessage.error('获取需求池数据失败，请稍后重试');
		// 如果接口失败，可以使用默认数据或保持空数组
	} finally {
		loading.value = false;
	}
};

// 根据需求类型获取分类
// const getCategoryByType = (needyType: string) => {
// 	const categoryMap: Record<string, string> = {
// 		'perf': '性能',
// 		'func': '功能',
// 		'agree': '协议',
// 		'dev': '开发',
// 		'public': '公共',
// 		'net': '弱网'
// 	};
// 	return categoryMap[needyType] || '其他';
// };

// 刷新需求池数据
const refreshDemandPool = () => {
	fetchDemandPoolData();
};

// 组件挂载时获取数据
onMounted(() => {
	fetchDemandPoolData();
	fetchTableHeader(); // 获取表头数据
	fetchTableData(); // 获取表格数据
});

// 事件处理方法
const handleCreateDemand = (demand: any) => {
	personalDemands.value.push(demand);
	// 刷新需求池
	refreshDemandPool();
};

const handleUpdateDemand = (updatedDemand: any) => {
	const index = personalDemands.value.findIndex((demand) => demand.id === updatedDemand.id);
	if (index > -1) {
		personalDemands.value[index] = updatedDemand;
	}
	refreshDemandPool();
};

const handleEditDemand = (demand: any) => {
	// 调用FunctionPanel的编辑方法
	functionPanelRef.value?.editDemand(demand);
};

const handleTaskClick = (taskDetail: any) => {
	selectedTask.value = taskDetail;
};

const handleUpdateTableData = () => {
	// tableData.value = data;
	fetchTableData();
};

const handleUpdatePersonalDemands = (demands?: any[]) => {
	if(demands){
		personalDemands.value = demands;
	}
	fetchDemandPoolData();
};

const handleFilterChange = (filterData: any) => {
	if (filterData.type === 'workType') {
		filterForm.value.workType = filterData.value;
	} else if (filterData.type === 'person') {
		filterForm.value.person = filterData.value;
		fetchTableData();
	} else if (filterData.type === 'business') {
		filterForm.value.business = filterData.value;
		fetchTableData();
	}
};

// 切换收缩状态
const toggleCollapse = () => {
	isCollapsed.value = !isCollapsed.value;
};
</script>

<style scoped lang="scss">
.arrange-task-container {
	background-color: #f5f7fa;
	height: calc(100vh - 80px);
	overflow: hidden;

	.arrange-task-padding {
		height: 100%;
		padding: 16px;
	}

	.panel-container {
		height: 100%;
		display: flex;
		position: relative;
	}

	.left-panel {
		width: 20%;
		overflow: visible;
		position: relative;
		transition: all 0.3s ease;
		background-color: #fff;
		border-radius: 4px;
		box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

		&.is-collapsed {
			width: 0;
			.panel-content {
				opacity: 0;
				visibility: hidden;
			}
		}

		.collapse-button {
			position: absolute;
			right: -10px;
			top: 50%;
			transform: translateY(-50%);
			width: 20px;
			height: 60px;
			background-color: #fff;
			//border: 1px solid #dcdfe6;
			border-left: none;
			border-radius: 0 4px 4px 0;
			display: flex;
			align-items: center;
			justify-content: center;
			cursor: pointer;
			z-index: 100;
			transition: all 0.3s ease;
			box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);

			&:hover {
				background-color: #f5f7fa;
			}

			.el-icon {
				font-size: 16px;
				color: #909399;
				display: flex;
				align-items: center;
				justify-content: center;
				width: 100%;
				height: 100%;

				svg {
					width: 1em;
					height: 1em;
				}
			}
		}

		.panel-content {
			height: 100%;
			transition: all 0.3s ease;
			opacity: 1;
			visibility: visible;
			padding: 16px;
			overflow: auto;
		}
	}

	.right-panel {
		flex: 1;
		overflow: hidden;
		margin-left: 16px;
		background-color: #fff;
		border-radius: 4px;
		box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
		padding: 16px;
	}
}
</style>

<style lang="scss">
.task-context-menu {
	background: white;
	border: 1px solid #dcdfe6;
	border-radius: 6px;
	box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.15);
	padding: 4px 0;
	z-index: 9999;
	min-width: 120px;

	.menu-item {
		padding: 10px 16px;
		cursor: pointer;
		font-size: 14px;
		color: #606266;
		transition: all 0.3s;

		&:hover {
			background-color: #f5f7fa;
			color: #409eff;
		}

		&#delete-task:hover {
			background-color: #fef0f0;
			color: #f56c6c;
		}
	}
}
</style>
