<template>
	<div class="leave-statistics-container layout-padding">
		<div class="leave-statistics-body" v-loading="loading" element-loading-text="Loading...">
			<el-form :model="formData" label-width="auto" class="leave-statistics-form">
				<el-form-item label="日期">
					<el-date-picker :editable="false" v-model="formData.date" :type="datePickerType" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
				</el-form-item>
				<el-form-item label="类型">
					<el-select v-model="formData.type" placeholder="请选择类型">
						<el-option label="周报" value="周报" />
						<el-option label="休假" value="休假" />
						<el-option label="加班" value="加班" />
					</el-select>
				</el-form-item>
				<el-form-item label="人员">
					<el-select v-model="formData.ids" multiple collapse-tags collapse-tags-tooltip :max-collapse-tags="1" placeholder="请选择人员" clearable>
						<el-option v-for="item in userOptions" :key="item.id" :label="item.real_name" :value="item.id" />
					</el-select>
				</el-form-item>
				<el-form-item>
					<el-button type="primary" @click="onSubmit" class="search-btn">查询</el-button>
				</el-form-item>
			</el-form>

			<!-- 查询内容  表格、图表 -->
			<div v-if="tableData.length > 0 && isGotData" class="leave-statistics-content" :style="{ marginTop: currentType === '周报' ? '0' : '50px' }">
				<div class="leave-statistics-table" :class="{ 'week-report': currentType === '周报' }">
					<el-button type="success" @click="exportToExcel" class="export-btn">{{ dataBtnName }}</el-button>
					<el-button-group v-if="currentType === '周报'" class="week-btn">
						<el-button class="group-btn" type="primary" @click="optionWeekClick(-1)"
							><el-icon class="el-icon--right"><ArrowLeft /></el-icon>上一周</el-button
						>
						<el-button type="primary" class="group-btn" @click="optionWeekClick(1)">
							下一周<el-icon class="el-icon--right"><ArrowRight /></el-icon>
						</el-button>
					</el-button-group>
					<el-table :data="tableData" border>
						<template v-if="currentType === '周报'">
							<el-table-column
								v-for="(val, keyName) in weekReportTableColumns"
								:key="keyName"
								:prop="keyName"
								:label="keyName"
								:width="keyName === '主要交付内容' ? '400px' : '150px'"
							>
								<template #default="scope">
									<el-tag v-if="keyName === '状态'" :type="getWeekReportStatus(scope.row['状态'])" class="date-tag">{{ scope.row['状态'] }}</el-tag>
								</template>
							</el-table-column>
						</template>

						<template v-else>
							<el-table-column prop="real_name" label="姓名" width="100px" />
							<el-table-column :prop="durationFieldName" :label="dateLabelName" width="100px" />
							<el-table-column :prop="dateFieldName" label="具体日期">
								<template #default="scope">
									<el-tag v-for="item in scope.row[dateFieldName]" :key="item.date" type="warning" class="date-tag">{{ item.date }}</el-tag>
								</template>
							</el-table-column>
						</template>
					</el-table>
				</div>
				<div v-if="currentType !== '周报'" class="leave-statistics-chart">
					<barChart ref="barChartRef" :data="barChartData" />
				</div>
			</div>

			<el-empty :image-size="200" v-if="isGotData && tableData.length === 0" />
		</div>
	</div>
</template>

<script setup lang="ts">
import { onMounted, ref, nextTick, computed, watch } from 'vue';
import { useAttendanceApi } from '/@/api/attendance/index';
import { useUserApi } from '/@/api/user/index';
import { formatDate } from '/@/utils/formatTime';
import barChart from './components/barChart.vue';
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue';
import * as XLSX from 'xlsx';
import { ElMessage } from 'element-plus';

// 休假、加班
const getDefaultDateRange = () => {
	const now = new Date();
	const start = new Date(now.getFullYear(), now.getMonth(), 1); // 本月第一天
	const end = new Date(now.getFullYear(), now.getMonth() + 1, 0); // 本月最后一天
	return [start, end];
};

// 周报
const getDefaultDate = () => {
	const now = new Date();
	const day = now.getDay() || 7; // 获取当前是周几，如果是周日则返回7
	const monday = new Date(now.setDate(now.getDate() - day + 1)); // 获取本周一
	console.log('monday---', monday);

	return monday;
};

interface FormDataType {
	date: Date | Date[] | null;
	type: string;
	ids: number[];
}

const formData = ref<FormDataType>({
	date: getDefaultDate(),
	type: '周报',
	ids: [],
});

// 添加一个新的响应式变量存储当前生效的类型
const currentType = ref('周报');
const datePickerType = ref('date');

// 监听类型变化
watch(
	() => formData.value.type,
	(newType) => {
		if (newType === '周报') {
			datePickerType.value = 'date';
			formData.value.date = new Date(getDefaultDate());
		} else {
			datePickerType.value = 'daterange';
			// 先设置为空，避免类型转换错误
			formData.value.date = null;
			// 使用nextTick确保日期选择器类型已更新
			nextTick(() => {
				const [start, end] = getDefaultDateRange();
				formData.value.date = [start, end];
			});
		}
	}
);

// 修改计算属性，使用 currentType 而不是 formData.type
const dateFieldName = computed(() => {
	return currentType.value === '休假' ? 'date_absence_days' : 'date_overtime_time';
});

const dateLabelName = computed(() => {
	return currentType.value === '休假' ? '休假天数' : '加班小时';
});

const dataBtnName = computed(() => {
	return currentType.value === '周报' ? '下载周报' : '导出Excel';
});

const durationFieldName = computed(() => {
	return currentType.value === '休假' ? 'absence_days' : 'overtime_time';
});

const weekReportTableColumns = computed(() => {
	return tableData.value[0];
});

interface TableDataItem {
	real_name: string;
	absence_days: number;
	overtime_time: number;
	date_absence_days: { date: string }[];
	date_overtime_time: { date: string }[];
	[key: string]: any; // Add index signature to allow dynamic keys
}

let tableData = ref<TableDataItem[]>([]);

const userApi = useUserApi();
const attendanceApi = useAttendanceApi();

// 添加用户选项数据
const userOptions = ref<any[]>([]);

let loading = ref(false);

// 获取用户列表的方法
const getUserOptions = async () => {
	try {
		const { code, data } = await userApi.getUserList({ page: 1, page_size: 1000, is_leader: 0 });
		if (code === 200) {
			userOptions.value = data;
		}
	} catch (error) {
		// console.error('获取用户列表失败:', error);
	}
};

let barChartData = ref({
	// title: '',
	// xAxis: { type: 'category', data: [] },
	// yAxis: { type: 'value' },
	// series: [{ data: [], type: 'bar' }],
});

const getBarChartData = () => {
	barChartData.value = {
		title: dateLabelName.value + '统计',
		xAxis: {
			type: 'category',
			data: tableData.value.map((item) => item.real_name),
		},
		yAxis: {
			type: 'value',
		},
		series: [
			{
				data: tableData.value.map((item) => item[durationFieldName.value]),
				type: 'bar',
			},
		],
	};
};

// const onDataBtnClick = () => {
// 	if (currentType.value === '周报') {
// 		ElMessage.error('暂未支持导出周报');
// 	} else {
// 		exportToExcel();
// 	}
// };
// 下载周报
const downloadWeeklyReport = async () => {
	try {
		let startDate, endDate;
		const currentDate = formData.value.date as Date;
		startDate = formatDate(currentDate, 'YYYY-mm-dd');
		endDate = formatDate(getLastDayOfWeek(currentDate), 'YYYY-mm-dd');
		const params = {
			start_date: startDate,
			end_date: endDate,
		};

		const response = await attendanceApi.downloadWeeklyReport(params);

		if (!response?.data) {
			throw new Error('下载失败');
		}

		const blob = new Blob([response.data], {
			type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
		});

		const filename = `周报-${params.start_date}-${params.end_date}.xlsx`;

		const url = window.URL.createObjectURL(blob);
		const link = document.createElement('a');
		link.href = url;
		link.download = filename;
		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);
		window.URL.revokeObjectURL(url);
	} catch (error) {
		ElMessage.error('下载失败，请稍后重试');
	}
};

// 导出Excel
const exportToExcel = () => {
	if (currentType.value === '周报') {
		downloadWeeklyReport();
	} else {
		// 准备导出数据
		const exportData = tableData.value.map((item) => ({
			姓名: item.real_name,
			[dateLabelName.value]: item[durationFieldName.value],
			具体日期: item[dateFieldName.value].map((item) => item.date).join(', '),
		}));

		// 创建工作簿
		const worksheet = XLSX.utils.json_to_sheet(exportData);
		const workbook = XLSX.utils.book_new();
		XLSX.utils.book_append_sheet(workbook, worksheet, currentType.value + '统计');

		// 导出文件
		XLSX.writeFile(workbook, `${currentType.value}统计${formatDate(new Date(), 'YYYY-mm-dd')}.xlsx`);
	}
};

let isGotData = ref(false);
const barChartRef = ref();

// 当前周的最后一天
const getLastDayOfWeek = (date: Date) => {
	const day = date.getDay() || 7;
	const sunday = new Date(date);
	sunday.setDate(date.getDate() + (7 - day));
	return sunday;
};

const onSubmit = async () => {
	loading.value = true;
	let startDate, endDate;
	if (formData.value.type === '周报') {
		const currentDate = formData.value.date as Date;
		startDate = formatDate(currentDate, 'YYYY-mm-dd');
		endDate = formatDate(getLastDayOfWeek(currentDate), 'YYYY-mm-dd');
	} else {
		console.log('formData.value.date',formData.value.date);		
		const dates = formData.value.date as Date[];
		startDate = formatDate(dates[0], 'YYYY-mm-dd');
		endDate = formatDate(dates[1], 'YYYY-mm-dd');
	}
	const ids = formData.value.ids.join(',');
	try {
		const res = await attendanceApi.getAbsenceStatistics({
			start_date: startDate,
			end_date: endDate,
			work_type: formData.value.type,
			ids,
			// work_type: '周报',
			// ids,
		});
		tableData.value = res.data;
		isGotData.value = true;
		// 查询成功后更新当前类型
		currentType.value = formData.value.type;

		getBarChartData();
		await nextTick();
		if (tableData.value.length > 0 && currentType.value !== '周报') {
			barChartRef.value.initChart();
		}
	} catch (error) {
		console.log('error------', error);

		ElMessage.error('获取统计数据失败');
		// 可以将错误信息记录到日志系统
	} finally {
		loading.value = false;
	}
};

const getWeekReportStatus = (status) => {
	let type = '';
	if (status === '进行中') {
		type = 'danger';
	} else if (status === '已完成') {
		type = 'success';
	}
	return type;
};

const optionWeekClick = (type: number) => {
	const currentDate = formData.value.date as Date;
	const newDate = new Date(currentDate);
	newDate.setDate(currentDate.getDate() + type * 7); // type为-1时减7天，为1时加7天
	formData.value.date = newDate;
	onSubmit();
};

onMounted(async () => {
	await getUserOptions();
	onSubmit();
});
</script>

<style scoped lang="scss">
.leave-statistics-container {
	.leave-statistics-body {
		position: relative;
		height: 100%;
		background-color: #fff;
		padding: 20px;
		border-radius: 10px;
		overflow-y: auto;
		.leave-statistics-form {
			max-width: 100% !important;
			display: flex;
			align-items: center;
			// flex-wrap: nowrap;
			.el-form-item {
				// flex-shrink: 0;
				margin-bottom: 22px !important;
				margin-left: 40px;
				&:first-child {
					margin-left: 0;
				}
			}
		}
		.search-btn {
			// margin-left: 20px;
		}
	}

	.leave-statistics-content {
		display: flex;
		justify-content: space-between;
		// align-items: flex-end;
		// margin-top: 50px;
		.leave-statistics-table {
			position: relative;
			width: 45%;
			&.week-report {
				width: 1150px;
			}
			.export-btn {
				margin-bottom: 10px;
			}
			.date-tag {
				margin-right: 10px;
				margin-bottom: 2px;
			}
			.week-btn {
				position: absolute;
				right: 0;
				top: 0;
			}
		}
		.leave-statistics-chart {
			width: 50%;
		}
	}

	:deep(.el-loading-spinner) {
		top: 10%;
	}
}
</style>
