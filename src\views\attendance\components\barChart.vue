<!-- 柱状图 -->
<template>
	<div class="bar-chart-container">
		<div class="" style="height: 400px" ref="barChartRef"></div>
	</div>
</template>

<script setup lang="ts" name="barChartCom">
import * as echarts from 'echarts';
import { reactive, onMounted, nextTick, onActivated, ref } from 'vue';

const barChartRef = ref();

const state = reactive({
	myChart: null as any,
});

const props = defineProps({
	data: {
		type: Object,
		default: () => {},
	},
});

const initChart = () => {
	const myChart = echarts.init(barChartRef.value);
	const option = {
		grid: {
			top: 60,
			left: 30,
			bottom: 20,
		},
		tooltip: {
			trigger: 'axis',
		},
		xAxis: {
			...props.data.xAxis,
			// 当只有一条数据时，设置轴线两侧的留白
			// axisTick: {
			// 	alignWithLabel: true,
			// },
			// // 控制坐标轴两侧的留白
			// boundaryGap: ['20%', '20%'],
		},
		yAxis: {
			type: 'value',
		},
		title: {
			text: props.data.title,
			textStyle: {
				fontSize: 16,
				fontWeight: 'normal',
				color: '#333',
			},
		},
		legend: {
			left: 'left',
			data: props.data.legend,
		},
		series: props.data.series.map((item: any) => {
			return {
				// name: item.name,
				type: item.type,
				data: item.data,
				label: {
					show: true,
					position: 'top',
					// formatter: percentageList.includes(props.data.key) ? '{c}%' : '{c}'
				},
				// 当数据量为1时，设置更小的宽度
				// ...(item.data.length === 1 && {
				// 	barWidth: '10%',
				// }),
			};
		}),
	};
	myChart.setOption(option);
	state.myChart = myChart;
};

// 页面加载时
onMounted(() => {
	initChart();
	initEchartsResize();
});

// 设置 echarts resize
const initEchartsResizeFun = () => {
	nextTick(() => {
		state.myChart.resize();
	});
};
// 设置 echarts resize
const initEchartsResize = () => {
	window.addEventListener('resize', initEchartsResizeFun);
};
// 由于页面缓存原因，keep-alive
onActivated(() => {
	initEchartsResizeFun();
});

// 导出initChart
defineExpose({
	initChart,
});
</script>

<style scoped lang="scss">
.bar-chart-container {
	// padding: 15px;
}
</style>
