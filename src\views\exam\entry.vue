<template>
	<div class="exam-entry_wrapper" v-loading="loading">
		<h2>新人入职测验</h2>
		<div class="exam-entry_content">
			<!-- 单选题 -->
			<div class="exam-entry_content_item">
				<h3>单选题</h3>
				<el-form :model="examEntryForm" label-width="auto">
					<el-form-item v-for="item in examEntryForm.single" :key="item.id" :label="item.question">
						<el-radio-group v-model="item.value">
							<el-radio v-for="option in item.options" :key="option.id" :label="option.option_value" :class="option.style">
								{{ option.option_value }}. {{ option.option_test }}
							</el-radio>
						</el-radio-group>
						<span v-if="item.score == 0" class="correct-answer">正确答案：{{ item.answer }}</span>
					</el-form-item>
				</el-form>
			</div>

			<!-- 多选题 -->
			<div class="exam-entry_content_item">
				<h3>多选题</h3>
				<el-form :model="examEntryForm" label-width="auto">
					<el-form-item v-for="item in examEntryForm.multiple" :key="item.id" :label="item.question">
						<el-checkbox-group v-model="item.value">
							<el-checkbox v-for="option in item.options" :key="option.id" :label="option.option_value" :class="option.style">
								{{ option.option_value }}. {{ option.option_test }}
							</el-checkbox>
						</el-checkbox-group>
						<span v-if="item.score == 0" class="correct-answer">正确答案：{{ item.answer }}</span>
					</el-form-item>
				</el-form>
			</div>
		</div>

		<el-button v-if="examResult == 0" class="submit-btn" type="primary" @click="submitExam">提交</el-button>
		<el-button v-else-if="examResult == 1" class="submit-btn" type="success" @click="goHome">返回首页</el-button>
		<el-button v-else-if="examResult == 2" class="submit-btn" type="primary" @click="reExam">重新测验</el-button>
	</div>
</template>

<script setup lang="ts" name="ExamEntry">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { useExamApi } from '/@/api/exam/index';
import { useUserInfo } from '/@/stores/userInfo';
import { storeToRefs } from 'pinia';
import { NextLoading } from '/@/utils/loading';

const examApi = useExamApi();

const router = useRouter();
const userStore = useUserInfo();
const { userInfos } = storeToRefs(userStore);

const loading = ref(false); // 加载状态
let examResult = ref(0); // 测验结果 0: 未提交 1: 通过 2: 未通过

// 定义option的类型
interface Option {
	id: number;
	option_value: string;
	option_test: string;
	style: string;
}

// 定义single的类型
interface Single {
	id: number;
	question: string;
	answer: string;
	score: number;
	value: string;
	options: Option[];
}

// 定义multiple的类型
interface Multiple {
	id: number;
	question: string;
	answer: string[];
	score: number;
	value: string[];
	options: Option[];
}
const examEntryForm = ref({
	single: [] as Single[],
	multiple: [] as Multiple[],
});

const submitExam = async () => {
	loading.value = true;
	let score = 0; // 单选题和多选题的得分
	examEntryForm.value.single.forEach((item) => {
		// 找出用户选择的那一项
		const choosedOption = item.options.find((option) => option.option_value == item.value) || { style: '' };

		if (item.value === item.answer) {
			// 回答正确
			item.score = 1;
			score += 1;

			// 找出正确答案那一项，添加correct样式
			choosedOption.style = 'correct';
		} else {
			// 回答错误
			item.score = 0;
			choosedOption.style = 'error';
		}

		// 判断该选项是否是正确答案
		// item.options.forEach((option) => {
		// 	if (option.value === item.answer) {
		// 		option.style = 'correct';
		// 	}
		// });
	});
	examEntryForm.value.multiple.forEach((item) => {
		item.answer = item.answer?.split('');
		if (hasSameValues(item.value, item.answer)) {
			item.score = 1;
			score += 1;

			// 回答正确，则将所选项添加correct样式
			item.options.forEach((option) => {
				if (item.value?.includes(option.option_value)) {
					option.style = 'correct';
				}
			});
		} else {
			item.score = 0;

			// 回答错误，则将所选项添加error样式
			item.options.forEach((option) => {
				if (item.value?.includes(option.option_value)) {
					option.style = 'error';
				}
			});
		}
		// 判断该选项是否是正确答案
		// item.options.forEach((option) => {
		// 	if (item.answer.includes(option.value)) {
		// 		option.style = 'correct';
		// 	}
		// });

		// 如果所选项不存在于answer中，则往options中添加error样式
		// item.value.forEach((val) => {
		// 	if (!item.answer.includes(val)) {
		// 		const errChooseOption = item.options.find((option) => option.value == val) || { style: '' };
		// 		errChooseOption.style = 'error';
		// 	}
		// });
	});
	console.log(examEntryForm.value);
	if (score >= 9) {
		ElMessage.success('恭喜你，测验通过！');
		// 更新用户测验状态
		await examApi.updateExamStatus({
			id: userInfos.value.userId,
			is_test: true,
		});
		// 刷新用户信息
		userStore.setUserInfos();
		examResult.value = 1;
	} else {
		ElMessage.error('很遗憾，测验未通过！');
		examResult.value = 2;
	}
	loading.value = false;
};

// 判读多选题的答案是否正确
const hasSameValues = (valueArr: string[], answerArr: string[]) => {
	if (!valueArr || !answerArr) return false;
	if (valueArr.length !== answerArr.length) return false;
	return valueArr.every((item) => answerArr.includes(item));
};

// 返回首页
const goHome = () => {
	router.replace('/');
};

// 重新测验
const reExam = () => {
	// router.replace('/exam/entry'); // 刷新页面
	// window.location.reload();
	examResult.value = 0;
	init();
};

const init = async () => {
	loading.value = true;
	const res = await examApi.getQuestion();
	examEntryForm.value.single = res.data.filter((item: any) => !item.is_many);
	examEntryForm.value.multiple = res.data.filter((item: any) => item.is_many);
	loading.value = false;
};

onMounted(() => {
	init();
	// 手动关闭loading
	NextLoading.done();
});
</script>

<style scoped lang="scss">
.exam-entry_wrapper {
	width: 100%;
	height: 100%;
	padding: 20px;
	h2 {
		margin-bottom: 20px;
		text-align: center;
	}
	.exam-entry_content {
		height: 85vh;
		overflow-y: auto;
		.exam-entry_content_item {
			padding: 20px 0;

			h3 {
				margin-bottom: 10px;
			}
			.el-form {
				padding: 0 20px;
				background-color: #fff;
			}
			.el-form-item {
				flex-direction: column;
				:deep(.el-form-item__label-wrap) {
					margin: 0 !important;
				}
				:deep(.el-form-item__content) {
					display: flex;
					flex-direction: column;
					align-items: flex-start;
					.correct-answer {
						color: #f02525;
					}
				}
			}
			.el-radio-group {
				display: flex;
				flex-direction: column;
				align-items: flex-start;
			}
			.el-checkbox-group {
				display: flex;
				flex-direction: column;
				align-items: flex-start;
			}
		}
	}
	.submit-btn {
		width: 150px;
		margin-top: 20px;
		margin-left: 50%;
		transform: translateX(-50%);
	}

	.correct {
		:deep(.el-radio__label) {
			color: #08b213;
		}
		:deep(.el-radio__input .el-radio__inner) {
			background-color: #08b213;
			border-color: #08b213;
			&::after {
				width: 4px;
				height: 4px;
				border-radius: 50%;
				background-color: #fff;
				content: '';
				position: absolute;
				left: 50%;
				top: 50%;
				transform: translate(-50%, -50%) scale(1);
				transition: transform 0.15s ease-in;
			}
		}
	}

	.error {
		:deep(.el-radio__label) {
			color: #f77979;
		}
		:deep(.el-radio__input .el-radio__inner) {
			background-color: #f77979;
			border-color: #f77979;
			&::after {
				width: 4px;
				height: 4px;
				border-radius: 50%;
				background-color: #fff;
				content: '';
				position: absolute;
				left: 50%;
				top: 50%;
				transform: translate(-50%, -50%) scale(1);
				transition: transform 0.15s ease-in;
			}
		}
	}

	.correct {
		:deep(.el-checkbox__label) {
			color: #08b213;
		}
		:deep(.el-checkbox__inner) {
			background-color: #08b213;
			border-color: #08b213;
			&::after {
				box-sizing: content-box;
				content: '';
				border: 1px solid #fff;
				border-left: 0;
				border-top: 0;
				height: 7px;
				left: 4px;
				position: absolute;
				top: 1px;
				transform: rotate(45deg) scaleY(1);
				width: 3px;
				transition: transform 0.15s ease-in 50ms;
				transform-origin: center;
			}
		}
	}
	.error {
		:deep(.el-checkbox__label) {
			color: #f56c6c;
		}
		:deep(.el-checkbox__inner) {
			background-color: #f56c6c;
			border-color: #f56c6c;
			&::after {
				box-sizing: content-box;
				content: '';
				border: 1px solid #fff;
				border-left: 0;
				border-top: 0;
				height: 7px;
				left: 4px;
				position: absolute;
				top: 1px;
				transform: rotate(45deg) scaleY(1);
				width: 3px;
				transition: transform 0.15s ease-in 50ms;
				transform-origin: center;
			}
		}
	}
}
</style>
