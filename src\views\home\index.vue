<template>
	<div class="home-container layout-padding">首页</div>
</template>

<script setup lang="ts" name="home"></script>

<style scoped lang="scss">
$homeNavLengh: 8;
.home-container {
	.el-card {
		:deep(.el-card__body) {
			height: 100%;
		}
		.tabs_box {
			height: 100%;
			:deep(.el-tabs__content) {
				height: calc(100% - 60px);
				.el-tab-pane {
					height: 100%;
				}
			}
		}
	}
}
</style>
