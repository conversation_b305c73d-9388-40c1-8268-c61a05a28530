<template>
	<el-form size="large" class="login-content-form">
		<el-form-item class="login-animation1">
			<el-input text placeholder="请输入账号" v-model="state.ruleForm.username" clearable autocomplete="off">
				<template #prefix>
					<el-icon class="el-input__icon"><ele-User /></el-icon>
				</template>
			</el-input>
		</el-form-item>
		<el-form-item class="login-animation2">
			<el-input
				:type="state.isShowPassword ? 'text' : 'password'"
				placeholder="请输入密码"
				v-model="state.ruleForm.password"
				autocomplete="off"
			>
				<template #prefix>
					<el-icon class="el-input__icon"><ele-Unlock /></el-icon>
				</template>
				<template #suffix>
					<i
						class="iconfont el-input__icon login-content-password"
						:class="state.isShowPassword ? 'icon-yincangmima' : 'icon-xianshimima'"
						@click="state.isShowPassword = !state.isShowPassword"
					>
					</i>
				</template>
			</el-input>
		</el-form-item>
        <el-form-item class="login-animation1">
			<el-input text placeholder="请输入姓名" v-model="state.ruleForm.real_name" clearable autocomplete="off">
                <template #prefix>
						<el-icon class="el-input__icon"><ele-Position /></el-icon>
				</template>
			</el-input>
		</el-form-item>
		<el-form-item class="login-animation4">
			<el-button type="primary" class="login-content-submit" round v-waves @click="onRegister" :loading="state.loading.signIn">
				<span>注 册</span>
			</el-button>
		</el-form-item>
	</el-form>
</template>

<script setup lang="ts" name="loginAccount">
import { reactive} from 'vue';
import { ElMessage } from 'element-plus';
// 登录相关api
import {useLoginApi} from '/@/api/login/index'
const loginApi = useLoginApi()
const emit = defineEmits(['changeLoginTab']);
const state = reactive({
	isShowPassword: false,
	ruleForm: {
		username: '',
		password: '',
		real_name: '',
	},
	loading: {
		signIn: false,
	},
});


// 登录
const onRegister = async () => {
    // 调用注册接口
	await loginApi.register(state.ruleForm)
  ElMessage.success('注册成功,切换到登录界面进行登录!')
    // 回到登录组件
    state.ruleForm={
        username: '',
		password: '',
		real_name: '', 
    }
    emit('changeLoginTab')

};

</script>

<style scoped lang="scss">
.login-content-form {
	margin-top: 20px;
	@for $i from 1 through 4 {
		.login-animation#{$i} {
			opacity: 0;
			animation-name: error-num;
			animation-duration: 0.5s;
			animation-fill-mode: forwards;
			animation-delay: calc($i/10) + s;
		}
	}
	.login-content-password {
		display: inline-block;
		width: 20px;
		cursor: pointer;
		&:hover {
			color: #909399;
		}
	}
	.login-content-code {
		width: 100%;
		padding: 0;
		font-weight: bold;
		letter-spacing: 5px;
	}
	.login-content-submit {
		width: 100%;
		letter-spacing: 2px;
		font-weight: 300;
		margin-top: 15px;
	}
}
</style>
