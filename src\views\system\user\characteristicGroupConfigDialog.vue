<template>
	<div class="characteristic-group-config-dialog">
		<el-dialog 
			title="特性小组配置" 
			v-model="state.dialog.isShowDialog" 
			width="800px" 
			@close="closeDialog"
			:close-on-click-modal="false"
		>
			<div class="dialog-content">
				<!-- 操作按钮 -->
				<div class="operation-buttons mb15">
					<el-button type="primary" @click="onOpenAddDialog" size="default">
						<el-icon><ele-Plus /></el-icon>
						新增特性小组
					</el-button>
				</div>

				<!-- 特性小组列表表格 -->
				<el-table 
					:data="state.tableData" 
					v-loading="state.loading" 
					style="width: 100%" 
					border
					stripe
				>
					<el-table-column type="index" label="序号" width="60" align="center" />
					<el-table-column prop="name" label="小组名称" show-overflow-tooltip />
				    <el-table-column prop="member_count" label="成员数量" width="100" align="center">
						<template #default="scope">
							<el-tag type="info" size="small">{{ scope.row.users?.length || 0 }}人</el-tag>
						</template>
					</el-table-column>
					<el-table-column prop="admin_name" label="小组管理员" show-overflow-tooltip>
						<template #default="scope">
							<span v-if="getGroupAdminNames(scope.row).length > 0">
								{{ getGroupAdminNames(scope.row).join(', ') }}
							</span>
							<span v-else class="text-gray">未设置</span>
						</template>
					</el-table-column>
					<!-- <el-table-column prop="created_at" label="创建时间" width="180" show-overflow-tooltip>
						<template #default="scope">
							<span v-if="scope.row.created_at">{{ formatDate(scope.row.created_at) }}</span>
							<span v-else>-</span>
						</template>
					</el-table-column> -->
					<el-table-column label="操作" width="200" align="center">
						<template #default="scope">
							<el-button 
								@click="onOpenEditDialog(scope.row)" 
								size="small" 
								type="primary" 
								text
							>
								设置管理员
							</el-button>
							<!-- <el-button 
								@click="onDeleteGroup(scope.row)" 
								size="small" 
								type="danger" 
								text
							>
								删除
							</el-button> -->
						</template>
					</el-table-column>
				</el-table>
			</div>

			<template #footer>
				<span class="dialog-footer">
					<el-button @click="closeDialog" size="default">关闭</el-button>
				</span>
			</template>
		</el-dialog>

		<!-- 新增/编辑特性小组弹窗 -->
		<el-dialog 
			:title="state.formDialog.title" 
			v-model="state.formDialog.isShowDialog" 
			width="500px" 
			@close="closeFormDialog"
			:close-on-click-modal="false"
		>
			<el-form 
				ref="formRef" 
				:model="state.formData" 
				:rules="formRules" 
				label-width="100px"
			>
				<el-form-item label="小组名称" prop="name">
					<el-input
						v-model="state.formData.name"
						placeholder="请输入特性小组名称"
						:readonly="state.formDialog.type === 'edit'"
						clearable
					/>
				</el-form-item>
				<el-form-item v-if="state.formDialog.type === 'edit'" label="小组管理员" prop="admin_id">
					<el-select
						v-model="state.formData.admin_id"
						placeholder="请选择小组管理员"
						clearable
						filterable
						style="width: 100%"
					>
						<el-option
							v-for="user in getCurrentGroupUsers()"
							:key="user.id"
							:label="user.real_name || user.username"
							:value="user.id"
						/>
					</el-select>
				</el-form-item>
			</el-form>

			<template #footer>
				<span class="dialog-footer">
					<el-button @click="closeFormDialog" size="default">取消</el-button>
					<el-button type="primary" @click="onSubmitForm" size="default">
						{{ state.formDialog.submitText }}
					</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts" name="CharacteristicGroupConfigDialog">
import { reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';
import { useUserApi } from '/@/api/user/index';

const userApi = useUserApi();
const formRef = ref();

// 定义事件
const emit = defineEmits(['refresh']);

// 定义接口类型
interface User {
	id: number;
	real_name: string;
	username: string;
	characteristic_group_admin?: number | null;
	business_name?: string;
	is_delete?: boolean;
	tutor_name?: string;
	charactor_group?: number[];
	charactor_group_admin_name?: string;
	company?: string;
	email?: string;
	mobile?: string;
	weixin?: string;
	is_admin?: boolean;
	create_time?: string;
	update_time?: string;
	is_test?: boolean;
	check_weekly_report?: boolean;
	business_type?: number;
	update_user?: number;
	tutor?: number;
	charactor_group_name?: string[];
}

interface CharacteristicGroup {
	id?: number;
	name: string;
	admin_id?: number;
	admin_name?: string;
	users?: User[];
	group_admin_user?: User[];
	created_at?: string;
}

// 定义状态
const state = reactive({
	dialog: {
		isShowDialog: false,
	},
	formDialog: {
		isShowDialog: false,
		title: '新增特性小组',
		submitText: '确定',
		type: 'add', // add | edit
	},
	tableData: [] as CharacteristicGroup[],
	loading: false,
	userList: [] as User[],
	formData: {
		id: undefined,
		name: '',
		admin_id: undefined,
	} as any,
});

// 表单验证规则
const formRules = reactive({
	name: [
		{ required: true, message: '请输入特性小组名称', trigger: 'blur' },
		// { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
	],
});



// 获取特性小组管理员名称
const getGroupAdminNames = (group: CharacteristicGroup) => {
	if (!group.group_admin_user || !group.id) return [];

	// 从 group_admin_user 数组中找到 characteristic_group_admin 等于当前小组 id 的用户
	const admins = group.group_admin_user.filter(user =>
		user.characteristic_group_admin === group.id
	);

	// 返回管理员的姓名数组
	return admins.map(admin => admin.real_name || admin.username).filter(Boolean);
};

// 获取特性小组列表
const getCharacteristicGroupList = async () => {
	state.loading = true;
	try {
		const res = await userApi.getCharacteristicGroupList();
		if (res.code === 200) {
			state.tableData = res.data || [];
		} else {
			ElMessage.error('获取特性小组列表失败');
		}
	} catch (error) {
		ElMessage.error('获取特性小组列表失败');
		console.error('获取特性小组列表失败:', error);
	} finally {
		state.loading = false;
	}
};

// 获取用户列表（从所有特性小组的users中汇总）
const getUserList = () => {
	const allUsers = new Map();

	// 从所有特性小组的users中收集用户
	state.tableData.forEach(group => {
		if (group.users) {
			group.users.forEach(user => {
				if (!user.is_delete) { // 过滤掉已删除的用户
					allUsers.set(user.id, user);
				}
			});
		}
	});

	state.userList = Array.from(allUsers.values());
};

// 获取当前编辑小组的用户列表
const getCurrentGroupUsers = () => {
	if (!state.formData.id) return [];

	const currentGroup = state.tableData.find(group => group.id === state.formData.id);
	if (!currentGroup || !currentGroup.users) return [];

	// 返回当前小组中未删除的用户
	return currentGroup.users.filter(user => !user.is_delete);
};

// 打开主弹窗
const openDialog = () => {
	state.dialog.isShowDialog = true;
	getCharacteristicGroupList();
};

// 关闭主弹窗
const closeDialog = () => {
	state.dialog.isShowDialog = false;
};

// 打开新增弹窗
const onOpenAddDialog = () => {
	state.formDialog.title = '新增特性小组';
	state.formDialog.submitText = '确定';
	state.formDialog.type = 'add';
	state.formDialog.isShowDialog = true;
	getUserList();
	resetFormData();
};

// 打开编辑弹窗（设置管理员）
const onOpenEditDialog = (row: CharacteristicGroup) => {
	state.formDialog.title = '设置小组管理员';
	state.formDialog.submitText = '保存';
	state.formDialog.type = 'edit';
	state.formDialog.isShowDialog = true;
	getUserList();

	// 获取当前小组的管理员ID
	const currentAdmin = row.group_admin_user?.find(user =>
		user.characteristic_group_admin === row.id
	);

	// 填充表单数据
	state.formData = {
		id: row.id,
		name: row.name,
		admin_id: currentAdmin?.id || undefined,
	};
};

// 关闭表单弹窗
const closeFormDialog = () => {
	state.formDialog.isShowDialog = false;
	resetFormData();
	if (formRef.value) {
		formRef.value.resetFields();
	}
};

// 重置表单数据
const resetFormData = () => {
	state.formData = {
		id: undefined,
		name: '',
		admin_id: undefined,
	};
};

// 提交表单
const onSubmitForm = async () => {
	if (!formRef.value) return;

	await formRef.value.validate(async (valid: boolean) => {
		if (valid) {
			try {
				const formData = { ...state.formData };
				let res;

				if (state.formDialog.type === 'add') {
					res = await userApi.addCharacteristicGroup(formData);
				} else {
					// 设置管理员
					res = await userApi.editUserInfo({
						id: formData.admin_id,
						characteristic_group_admin: formData.id
					},'patch');
				}

				if (res.code === 200) {
					ElMessage.success(state.formDialog.type === 'add' ? '新增成功' : '设置管理员成功');
					closeFormDialog();
					getCharacteristicGroupList();
					emit('refresh'); // 刷新父组件数据
				} else {
					ElMessage.error(res.message || '操作失败');
				}
			} catch (error) {
				ElMessage.error('操作失败');
				console.error('提交表单失败:', error);
			}
		}
	});
};



// 暴露方法
defineExpose({
	openDialog,
});
</script>

<style scoped lang="scss">
.characteristic-group-config-dialog {
	.dialog-content {
		.operation-buttons {
			display: flex;
			justify-content: flex-start;
		}
	}
	
	.text-gray {
		color: #999;
	}
}
</style>
