<template>
	<div class="system-role-dialog-container">
		<el-dialog :title="state.dialog.title" v-model="state.dialog.isShowDialog" width="769px" @close="closeDialog">
			<el-form ref="userDialogFormRef" :model="state.ruleForm" :rules="rules" size="default" label-width="90px">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="账号" prop="username">
							<el-input v-model="state.ruleForm.username" placeholder="请输人账号" clearable></el-input>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" v-if="state.dialog.type !== 'edit'">
						<el-form-item label="密码" prop="password">
							<el-input v-model="state.ruleForm.password" placeholder="请输密码" type="password" show-password></el-input>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="姓名" prop="real_name">
							<el-input v-model="state.ruleForm.real_name" placeholder="请输人员姓名" clearable></el-input>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="所属企业" prop="company">
							<el-select v-model="state.ruleForm.company" placeholder="请选择" clearable class="w100">
								<el-option label="腾讯" value="腾讯">腾讯</el-option>
								<el-option label="中电金信" value="中电金信">中电金信</el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="工作组" prop="group">
							<el-select v-model="state.ruleForm.group" placeholder="请选择" clearable class="w100">
								<el-option v-for="item in groupEnum" :key="item.value" :label="item.label" :value="item.value">{{ item.label }}</el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<!-- 新增导师选择 -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" v-if="state.ruleForm.company !== '腾讯'">
						<el-form-item label="导师" prop="tutor">
							<el-select v-model="state.ruleForm.tutor" placeholder="请选择" clearable class="w100">
								<el-option v-for="item in tutorOptions" :key="item.value" :label="item.label" :value="item.value" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="手机">
							<el-input v-model="state.ruleForm.mobile" placeholder="请输入手机号" clearable></el-input>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="企业微信">
							<el-input v-model="state.ruleForm.weixin" placeholder="请输入企业微信" clearable></el-input>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="特性小组">
							<el-select v-model="state.ruleForm.charactor_group_name" placeholder="请选择特性小组" clearable class="w100" multiple>
								<el-option v-for="item in state.characteristicGroupOptions" :key="item.id" :label="item.name" :value="item.name" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col v-if="isAdmin" :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="查看考勤权限" label-width="130px">
							<el-switch v-model="state.isCheckWeeklyReport" @change="checkWeeklyReportChange" />
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="onCancel" size="default">取 消</el-button>
					<el-button type="primary" @click="onSubmit(userDialogFormRef)" size="default">{{ state.dialog.submitTxt }}</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts" name="systemRoleDialog">
import { reactive, ref } from 'vue';
import { useUserApi } from '/@/api/user/index';
import { ElMessage } from 'element-plus';
import { useUserInfo } from '/@/stores/userInfo';
import { storeToRefs } from 'pinia';
const userApi = useUserApi();
const stores = useUserInfo();
const { userInfos } = storeToRefs(stores);

// 定义子组件向父组件传值/事件
const emit = defineEmits(['refresh']);
const userDialogFormRef = ref();

interface Group {
	id: number;
	name: string;
}
const isAdmin = userInfos.value.isAdmin;
// 定义变量内容
const state = reactive({
	ruleForm: {
		id: 0,
		check_weekly_report: false,
		username: '',
		password: '',
		real_name: '',
		email: '',
		mobile: '',
		company: '',
		weixin: '',
		group: '',
		charactor_group_name: [],
		tutor: '', // 新增导师字段
	},
	dialog: {
		isShowDialog: false,
		type: '',
		title: '',
		submitTxt: '',
	},
	characteristicGroupOptions: [] as Group[],
	isCheckWeeklyReport: false,
});
const rules = reactive<any>({
	username: [{ required: true, message: '请输入用户名!', trigger: 'blur' }],
	real_name: [{ required: true, message: '请输入真实姓名', trigger: 'blur' }],
	password: [{ required: true, message: '请输入用户登录密码!', trigger: 'blur' }],
	company: [{ required: true, message: '请选择所属企业!', trigger: 'blur' }],
	group: [{ required: true, message: '请选择工作组!', trigger: 'blur' }],
});
// 工作组的枚举
const groupEnum: any = ref([]);
// 打开弹窗
const openDialog = (type: string, row: any) => {
	state.dialog.type = type;
	getUserGroupFn();
	getTutorData();
	getGroupList();
	if (type === 'edit') {
		row.group = row.business_type;
		state.ruleForm = JSON.parse(JSON.stringify(row));
		state.isCheckWeeklyReport = state.ruleForm.check_weekly_report;
		state.dialog.title = '修改人员信息';
		state.dialog.type = type;
		state.dialog.submitTxt = '修 改';
	} else {
		state.dialog.title = '新增人员';
		state.dialog.submitTxt = '新 增';
		// 清空表单，此项需加表单验证才能使用
		// nextTick(() => {
		// 	roleDialogFormRef.value.resetFields();
		// });
	}
	state.dialog.isShowDialog = true;
};
// 关闭弹窗
const closeDialog = () => {
	state.dialog.isShowDialog = false;
	clearForm();
	userDialogFormRef.value.resetFields();
};
const clearForm = () => {
	state.ruleForm = {
		username: '',
		password: '',
		real_name: '',
		email: '',
		mobile: '',
		company: '',
		weixin: '',
		group: '',
		tutor: '',
		charactor_group_name: [],
	};
};
// 获取工作组
const getUserGroupFn = async () => {
	const res = await userApi.getUserGroup();
	const arr: any = [];
	res.data?.forEach((item: any) => {
		arr.push({
			label: item.name,
			value: item.id,
		});
	});
	groupEnum.value = arr;
};

// 获取特性小组列表
const getGroupList = async () => {
	if (state.characteristicGroupOptions.length > 0) return;
	const res = await userApi.updateCharacteristicGroup({}, 'get');
	if (res.code === 200) {
		state.characteristicGroupOptions = res.data;
	}
};

// 分配查看考勤权限
const checkWeeklyReportChange = async (e: Boolean) => {
	const { code } = await userApi.editCheckWeeklyReportPermission(state.ruleForm.id, { check_weekly_report: e });
	if (code === 200) {
		ElMessage.success('设置成功');
		emit('refresh');
	}
};

// 定义导师选项
const tutorOptions: any = ref([]);
// 获取导师数据
const getTutorData = async () => {
	const { code, data } = await userApi.getUserList({ page: 1, page_size: 100, is_leader: 1 });
	if (code === 200) {
		const arr: any = [];
		data.forEach((item: any) => {
			arr.push({ label: item.real_name, value: item.id });
		});
		tutorOptions.value = arr;
	} else {
		tutorOptions.value = [];
	}
};
// 取消
const onCancel = () => {
	closeDialog();
};
// 提交
const onSubmit = async (formEl: any) => {
	const params = JSON.parse(JSON.stringify(state.ruleForm));
	const arrgroupNameIds = params.charactor_group_name.map((item1: any) => {
		return state.characteristicGroupOptions.find((item2: any) => item2.name === item1)?.id;
	});
	params.charactor_group = arrgroupNameIds;
	if (state.dialog.type === 'add') {
		// 新增
		// console.log('params',params);
		if (!formEl) return;
		await formEl.validate(async (valid: any) => {
			if (valid) {
				await userApi.addUser(params); // 新增接口
				ElMessage.success('新增成功!');
				closeDialog();
				emit('refresh');
			} else {
				return ElMessage.warning('请完善表单内容!');
			}
		});
	} else {
		await formEl.validate(async (valid: any) => {
			if (valid) {
				if (params.password) delete params.password; // 修改时不传密码
				params.business_type = params.group;
				await userApi.editUserInfo(params); // 编辑时传id
				ElMessage.success('修改成功!');
				closeDialog();
				emit('refresh');
			} else {
				return ElMessage.warning('请完善表单内容!');
			}
		});
	}
};

// 暴露变量
defineExpose({
	openDialog,
});
</script>

<style scoped lang="scss">
.system-role-dialog-container {
	.menu-data-tree {
		width: 100%;
		border: 1px solid var(--el-border-color);
		border-radius: var(--el-input-border-radius, var(--el-border-radius-base));
		padding: 5px;
	}
}
</style>
