<template>
	<div>
		<el-dialog :title="state.dialog.title" v-model="state.dialog.isShowDialog" width="550px" @close="closeDialog">
			<el-tabs v-model="state.activeTab">
				<el-tab-pane label="特性小组" name="group">
					<el-form :model="state.form" :rules="rules" ref="formRef" label-width="100px">
						<el-form-item label="小组名称" prop="groupName" size="default">
							<el-input v-model="state.form.groupName" placeholder="请输入特性小组名称"></el-input>
						</el-form-item>
					</el-form>
				</el-tab-pane>
				<el-tab-pane label="特性小组管理员" name="admin">
					<el-form :model="state.adminForm" :rules="adminRules" ref="adminFormRef" label-width="100px" size="default">
						<!-- <el-form-item label="管理员名称" prop="adminName">
							<el-input v-model="state.adminForm.adminName" placeholder="请输入管理员名称"></el-input>
						</el-form-item> -->
						<el-form-item label="用户" prop="userId">
							<el-select v-model="state.adminForm.userId" placeholder="请选择用户" style="width: 100%" @visible-change="getUserList">
								<el-option v-for="user in state.userList" :key="user.id" :label="user.real_name" :value="user.id" />
							</el-select>
						</el-form-item>
						<el-form-item label="特性小组" prop="characteristic_group">
							<el-select
								v-model="state.adminForm.characteristic_group"
								placeholder="请选择特性小组"
								style="width: 100%"
								@visible-change="(visible: boolean) => getGroupList(visible, 'get')"
							>
								<el-option v-for="group in state.groupList" :key="group.id" :label="group.name" :value="group.id" />
							</el-select>
						</el-form-item>
					</el-form>
				</el-tab-pane>
			</el-tabs>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="closeDialog" size="default">取 消</el-button>
					<el-button type="primary" @click="submitForm" size="default">确 定</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts">
import { reactive, ref, defineEmits } from 'vue';
import type { FormInstance } from 'element-plus';
import { ElMessage } from 'element-plus';
import { useUserApi } from '/@/api/user/index';
const userApi = useUserApi();

const formRef = ref<FormInstance>();
const adminFormRef = ref<FormInstance>();
const emit = defineEmits(['refresh']);

interface User {
	id: number;
	real_name: string;
}

interface Group {
	id: number;
	name: string;
}

const state = reactive({
	dialog: {
		title: '新增',
		isShowDialog: false,
	},
	activeTab: 'group',
	form: {
		groupName: '',
	},
	adminForm: {
		// adminName: '',
		userId: '',
		characteristic_group: '',
	},
	userList: [] as User[],
	groupList: [] as Group[],
});

const rules = {
	groupName: [
		{ required: true, message: '请输入特性小组名称', trigger: 'blur' },
		// { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' },
	],
};

const adminRules = {
	// adminName: [{ required: true, message: '请输入管理员名称', trigger: 'blur' }],
	userId: [{ required: false, message: '请选择用户', trigger: 'change' }],
	characteristic_group: [{ required: false, message: '请选择特性小组', trigger: 'change' }],
};

// 获取用户列表
const getUserList = async () => {
	if (state.userList.length > 0) return;
	const res = await userApi.getUserList({});
	if (res.code === 200) {
		state.userList = res.data;
	}
};

// 获取特性小组列表
const getGroupList = async (visible: boolean, method: string) => {
	if (!visible) return;
	const res = await userApi.updateCharacteristicGroup({}, method);
	if (res.code === 200) {
		state.groupList = res.data;
	}
};

// 打开弹窗
const openDialog = () => {
	state.dialog.isShowDialog = true;
};

const closeDialog = () => {
	state.dialog.isShowDialog = false;
	state.form.groupName = '';
	state.adminForm = {
		// adminName: '',
		userId: '',
		characteristic_group: '',
	};
	state.activeTab = 'group';
};

// 创建特性小组
const submitForm = async () => {
	if (state.activeTab === 'group') {
		if (!formRef.value) return;
		await formRef.value.validate(async (valid) => {
			if (valid) {
				const res = await userApi.updateCharacteristicGroup(
					{
						name: state.form.groupName,
					},
					'post'
				);
				if (res.code === 200) {
					ElMessage.success('创建特性小组成功');
					closeDialog();
					emit('refresh');
				} else {
					ElMessage.error('创建特性小组失败');
				}
			}
		});
	} else {
		if (!adminFormRef.value) return;
		await adminFormRef.value.validate(async (valid) => {
			if (valid) {
				const res = await userApi.editUserInfo(
					{
						// name: state.adminForm.adminName,
						id: state.adminForm.userId,
						characteristic_group_admin: state.adminForm.characteristic_group,
					},
					'patch'
				);
				if (res.code === 200) {
					ElMessage.success('创建管理员成功');
					closeDialog();
				} else {
					ElMessage.error('创建管理员失败');
				}
			}
		});
	}
};

// 暴露变量
defineExpose({
	openDialog,
});
</script>

<style scoped>
.dialog-footer {
	display: flex;
	justify-content: flex-end;
	gap: 10px;
}
</style>
