<template>
	<div class="system-role-dialog-container">
		<el-dialog :title="state.dialog.title" v-model="state.dialog.isShowDialog" width="550px" @close="closeDialog">
			<el-form ref="userDialogFormRef" :model="state.ruleForm" :rules="rules" size="default" label-width="110px">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item label="工作组名称" prop="name">
							<el-input v-model="state.ruleForm.name" placeholder="请输入工作组名称" clearable></el-input>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="onCancel" size="default">取 消</el-button>
					<el-button type="primary" @click="onSubmit(userDialogFormRef)" size="default">{{ state.dialog.submitTxt }}</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts" name="systemRoleDialog">
import { reactive, ref } from 'vue';
import { useUserApi } from '/@/api/user/index';
import { ElMessage } from 'element-plus';
const userApi = useUserApi();
// 定义子组件向父组件传值/事件
// const emit = defineEmits(['refresh']);
const userDialogFormRef = ref();
// 定义变量内容
const state = reactive({
	ruleForm: {
		name: '',
	},
	dialog: {
		isShowDialog: false,
		type: '',
		title: '添加工作组',
		submitTxt: '确定',
	},
});
const rules = reactive<any>({
	name: [{ required: true, message: '请输入分组名称!', trigger: 'blur' }],
});
// 打开弹窗
const openDialog = () => {
	state.dialog.isShowDialog = true;
};
// 关闭弹窗
const closeDialog = () => {
	state.dialog.isShowDialog = false;
	clearForm();
	userDialogFormRef.value.resetFields();
};
const clearForm = () => {
	state.ruleForm = {
		name: '',
	};
};

// 取消
const onCancel = () => {
	closeDialog();
};
// 提交
const onSubmit = async (formEl: any) => {
	const params = JSON.parse(JSON.stringify(state.ruleForm));
	await formEl.validate(async (valid: any) => {
		if (valid) {
            // console.log('params',params);           
			await userApi.addUserGroup(params); // 新增接口
			ElMessage.success('新增成功!');
			closeDialog();
		}
	});
};

// 暴露变量
defineExpose({
	openDialog,
});
</script>

<style scoped lang="scss">
.system-role-dialog-container {
	.menu-data-tree {
		width: 100%;
		border: 1px solid var(--el-border-color);
		border-radius: var(--el-input-border-radius, var(--el-border-radius-base));
		padding: 5px;
	}
}
</style>
