<template>
	<div class="system-role-container layout-padding">
		<div class="system-role-padding layout-padding-auto layout-padding-view">
			<div class="system-user-search mb15">
				<div>
					<el-input
						v-model="state.tableData.param.real_name"
						size="default"
						placeholder="请输入用户姓名"
						style="max-width: 180px"
						clearable
						@clear="handleClear"
					>
					</el-input>
					<el-button style="margin-right: 20px" size="default" type="primary" class="ml10" @click="getTableData({ type: 'check' })">
						<el-icon>
							<ele-Search />
						</el-icon>
						查询
					</el-button>
					<!-- <el-button size="default" type="success" class="ml10" @click="addGroupAdmin">
						<el-icon>
							<ele-FolderAdd />
						</el-icon>
						特性小组
					</el-button> -->
					<el-button size="default" type="success" class="ml10" @click="onOpenCharacteristicGroupConfig">
						<el-icon>
							<ele-FolderAdd />
						</el-icon>
						特性小组配置
					</el-button>
				</div>
				<div>
					<el-button size="default" type="info" class="ml10" @click="onOpenGroup">新增工作组</el-button>
				</div>
			</div>
			<div class="group-table_wrapper">
				<div class="table-header">
					<el-table :data="[]" style="width: 100%" class="header-table">
						<el-table-column type="index" label="序号" width="60" />
						<el-table-column prop="real_name" label="姓名"></el-table-column>
						<el-table-column prop="business_name" label="工作组"></el-table-column>
						<el-table-column prop="username" label="账号名称"></el-table-column>
						<el-table-column prop="characteristic_group_admin" label="是否特性小组组长"></el-table-column>
						<el-table-column prop="company" label="所属企业"></el-table-column>
						<el-table-column prop="tutor_name" label="导师"></el-table-column>
						<el-table-column prop="mobile" label="手机号"></el-table-column>
						<el-table-column prop="weixin" label="企业微信"></el-table-column>
						<el-table-column label="操作" width="200"></el-table-column>
					</el-table>
				</div>
				<div class="table-content">
					<div v-for="(_, groupName) in state.tableData.data" :key="groupName" class="group-table-container">
						<div class="group-title" :class="{ 'is-group-title-expanded': isGroupExpanded(groupName) }">
							<el-icon :size="16" class="group-title-icon" :class="{ 'is-expanded': isGroupExpanded(groupName) }" @click="toggleGroup(groupName)">
								<CaretBottom />
							</el-icon>
							<el-tag :color="getGroupColor(groupName)" style="color: #333">{{ groupName || '未分组' }}</el-tag>
						</div>
						<el-table
							:data="state.tableData.data[groupName][0].users"
							v-loading="state.tableData.loading"
							style="width: 100%"
							class="mb20"
							:show-header="false"
							border
							v-show="isGroupExpanded(groupName)"
						>
							<el-table-column type="index" width="60" />
							<el-table-column prop="real_name" show-overflow-tooltip></el-table-column>
							<el-table-column prop="business_name" label="工作组"> </el-table-column>
							<el-table-column prop="username" show-overflow-tooltip></el-table-column>
							<el-table-column prop="characteristic_group_admin">
								<template #default="scope">
									<el-tag v-if="scope.row.characteristic_group_admin" type="success">是</el-tag>
									<el-tag v-else type="info">否</el-tag>
								</template>
							</el-table-column>
							<el-table-column prop="company" show-overflow-tooltip></el-table-column>
							<el-table-column prop="tutor_name" show-overflow-tooltip>
								<template #default="scope">
									<span v-if="scope.row.tutor_name">{{ scope.row.tutor_name }}</span>
									<span v-else>-</span>
								</template>
							</el-table-column>
							<el-table-column prop="mobile" show-overflow-tooltip></el-table-column>
							<el-table-column prop="weixin" show-overflow-tooltip></el-table-column>
							<el-table-column width="200">
								<template #default="scope">
									<el-button @click="onOpenEditUser('edit', scope.row)" size="default" text type="primary">修改信息</el-button>
									<el-button @click="onRowDel(scope.row)" size="default" text :type="scope.row.is_delete ? 'info' : 'danger'">
										{{ scope.row.is_delete ? '已停用' : '停用' }}
									</el-button>
								</template>
							</el-table-column>
						</el-table>
					</div>
				</div>
			</div>
			<!-- <el-pagination
				@size-change="onHandleSizeChange"
				@current-change="onHandleCurrentChange"
				class="mt15"
				:pager-count="5"
				:page-sizes="[10, 20, 30]"
				v-model:current-page="state.tableData.param.page"
				background
				v-model:page-size="state.tableData.param.page_size"
				layout="total, sizes, prev, pager, next, jumper"
				:total="state.tableData.total"
			>
			</el-pagination> -->
		</div>
		<UserDialog ref="userDialogRef" @refresh="getTableData()" />
		<GorupDialog ref="groupDialogRef"></GorupDialog>
		<GroupAdminDialog ref="groupAdminDialogRef" @refresh="getTableData()"></GroupAdminDialog>
		<SetPassWord ref="setpassWordRef" @refresh="getTableData()"></SetPassWord>
		<CharacteristicGroupConfigDialog ref="characteristicGroupConfigDialogRef" @refresh="getTableData()"></CharacteristicGroupConfigDialog>
	</div>
</template>

<script setup lang="ts" name="systemRole">
import { defineAsyncComponent, reactive, onMounted, ref } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { useUserApi } from '/@/api/user/index';
import { CaretBottom } from '@element-plus/icons-vue';
const userApi = useUserApi();
// 引入组件
const UserDialog = defineAsyncComponent(() => import('./dialog.vue'));
const GorupDialog = defineAsyncComponent(() => import('./groupDialog.vue'));
const SetPassWord = defineAsyncComponent(() => import('./setPassWord.vue'));
const GroupAdminDialog = defineAsyncComponent(() => import('./groupAdminDialog.vue')); // 新增小组管理员
const CharacteristicGroupConfigDialog = defineAsyncComponent(() => import('./characteristicGroupConfigDialog.vue')); // 特性小组配置
// 定义变量内容
const userDialogRef = ref();
const groupDialogRef = ref();
const setpassWordRef = ref();
const groupAdminDialogRef = ref();
const characteristicGroupConfigDialogRef = ref();
const state = reactive<any>({
	tableData: {
		data: {}, // 由数组改为对象，key为分组名
		total: 0,
		loading: false,
		param: {
			real_name: '',
		},
	},
	// 添加展开/收起状态
	expandedGroups: new Set<string>(),
});

// 获取工作组对应的颜色
const getGroupColor = (groupName: string | number) => {
	// 生成一组可选颜色
	const palette = ['#d2cbf7', '#f7d2cb', '#cbf7d2', '#f7f4cb', '#cbe0f7', '#f7cbe0', '#e0cbf7', '#cbf7e0', '#f7e0cb', '#e0f7cb'];
	// 获取所有分组名称并排序
	const groupNames = Object.keys(state.tableData.data).sort();
	// 找到当前分组在排序后的数组中的索引
	const index = groupNames.indexOf(String(groupName));
	// 使用索引分配颜色，如果索引超出调色板范围则循环使用颜色
	return palette[index % palette.length];
};

// 切换表格展开/收起状态
const toggleGroup = (groupName: string | number) => {
	const name = String(groupName);
	if (state.expandedGroups.has(name)) {
		state.expandedGroups.delete(name);
	} else {
		state.expandedGroups.add(name);
	}
};

// 检查表格是否展开
const isGroupExpanded = (groupName: string | number) => {
	return state.expandedGroups.has(String(groupName));
};

// 按 name 字段分组
const groupByName = (data: any[]) => {
	const groups: { [key: string]: any[] } = {};
	data.forEach((item) => {
		const groupName = item.name || '未分组';
		if (!groups[groupName]) {
			groups[groupName] = [];
		}
		groups[groupName].push(item);
	});
	return groups;
};

// 初始化表格数据
const getTableData = async ({ type = '' } = {}) => {
	state.tableData.loading = true;
	try {
		const { code, data } = await userApi.getCharacteristicGroup(state.tableData.param);
		if (code === 200) {
			state.tableData.data = groupByName(data);
			state.expandedGroups = new Set(
				Object.entries(state.tableData.data)
					.filter(([, group]) => (group as any)[0].users && (group as any)[0].users.length > 0)
					.map(([groupName]) => groupName)
			);
			state.tableData.loading = false;
		} else {
			ElMessage.error('请求接口错误,请稍后再试!');
			state.tableData.loading = false;
		}
	} catch {
		state.tableData.data = {};
		state.tableData.loading = false;
	}
};
// 打开新增角色弹窗
// const onOpenAddRole = (type: string) => {
// 	userDialogRef.value.openDialog(type);
// };

// 新增小组管理员
const addGroupAdmin = () => {
	groupAdminDialogRef.value.openDialog();
};
// 打开修改角色弹窗
const onOpenEditUser = (type: string, row: Object) => {
	userDialogRef.value.openDialog(type, row);
};
// 停用/启用 角色
const onRowDel = async (row: any) => {
	if (row.is_delete) {
		await userApi.reUseUser(row.id, { is_delete: false });
		ElMessage.success('启用成功');
		getTableData();
		return;
	}
	ElMessageBox.confirm(`确认要停用该人员吗?`, '提示', {
		confirmButtonText: '确认',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await userApi.delUserInfo(row.id, { is_delete: true });
			ElMessage.success('停用成功');
			getTableData();
		})
		.catch(() => {});
};
// 分页改变
// const onHandleSizeChange = (val: number) => {
// 	state.tableData.param.page_size = val;
// 	getTableData();
// };
// 分页改变
// const onHandleCurrentChange = (val: number) => {
// 	state.tableData.param.page = val;
// 	getTableData();
// };
// 清空查询
const handleClear = () => {
	state.tableData.param.page = 1;
	getTableData();
};
//打开工作组弹框
const onOpenGroup = () => {
	groupDialogRef.value.openDialog();
};

// 打开特性小组配置弹窗
const onOpenCharacteristicGroupConfig = () => {
	characteristicGroupConfigDialogRef.value.openDialog();
};
// 打开修改密码弹窗
// const onOpensetPassWordDialog = (row: any) => {
// 	setpassWordRef.value.openPwdDialog(row);
// };
// 页面加载时
onMounted(() => {
	getTableData();
});
// 测试数据
</script>

<style scoped lang="scss">
.system-role-container {
	.system-role-padding {
		padding: 15px;
		.el-table {
			flex: 1;
		}
	}
	.system-user-search {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}
	.group-table_wrapper {
		overflow: auto;
		&::-webkit-scrollbar {
			display: none;
		}
		-ms-overflow-style: none;
		scrollbar-width: none;
		.table-header {
			position: sticky;
			top: 0;
			z-index: 99;
			background: #fff;
			.header-table {
				:deep(.el-scrollbar) {
					height: 0;
				}
			}
		}
		.table-content {
			padding-top: 20px;
			.group-table-container {
				position: relative;
				border-left: 1px solid #ebeef5;
				border-right: 1px solid #ebeef5;
				&::before {
					position: absolute;
					top: -20px;
					left: 0;
					content: '';
					display: block;
					width: 100%;
					height: 20px;
					background: #f8f8f8;
					border-bottom: 1px solid #fff;
					z-index: 9;
				}
				.group-title {
					display: flex;
					align-items: center;
					padding: 12px;
					border-bottom: 1px solid #ebeef5;
					&:not(.is-group-title-expanded) {
						margin-bottom: 20px;
					}
					.group-title-icon {
						margin-right: 20px;
						cursor: pointer;
						transition: transform 0.3s;
						&.is-expanded {
							transform: rotate(0deg);
						}
						&:not(.is-expanded) {
							transform: rotate(-90deg);
							// margin-bottom: 20px;
						}
					}
				}
			}
			.group_name-tag {
				margin-bottom: 4px;
				color: #333;
				&:last-child {
					margin-bottom: 0;
				}
			}
		}
	}
}
</style>
