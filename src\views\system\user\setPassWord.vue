<template>
	<div class="system-user-dialog-container">
		<el-dialog v-model="state.isSetPwdDialog" title="修改登录密码" width="30%">
			<el-form ref="userPwdFormRef" :model="state.pwdForm" :rules="rules" @submit.native.prevent label-width="80px">
				<el-form-item label="新密码" prop="password">
					<el-input v-model="state.pwdForm.password" clearable type="password" placeholder="请输入新密码" show-password />
				</el-form-item>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="closeDialog">取 消</el-button>
					<el-button type="primary" @click="confirmChangePwd(userPwdFormRef)"> 确 认 </el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts" name="setPwdDialog">
import { reactive, ref } from 'vue';
// 用户信息相关api
import { useUserApi } from '/@/api/user/index';
import { ElMessage, FormInstance } from 'element-plus';
const userApi = useUserApi();
const userPwdFormRef = ref();
// 定义子组件向父组件传值/事件
const emit = defineEmits(['refresh']);
const userId:any = ref('')
const state = reactive({
	isSetPwdDialog: false,
	pwdForm: {
		password: '',
	},
});
const rules = reactive<any>({
	password: [{ required: true, message: '请输入用户登录密码!', trigger: 'blur' }],
});
const closeDialog = () => {
	state.pwdForm.password = '';
	state.isSetPwdDialog = false;
	userPwdFormRef.value.resetFields();
};
const confirmChangePwd = async (formEl: FormInstance | undefined) => {
	if (!formEl) return;
	await formEl.validate(async (valid) => {
		if (valid) {
			try{
				await userApi.updatePasswordForAdmin(state.pwdForm,userId.value)
				ElMessage.success('修改密码成功!');
				emit('refresh');
				closeDialog()
			}catch(error){
				ElMessage.error('当前用户无权限修改密码!');				
			}
		} else {
			ElMessage.warning('请输入新密码!');
		}
		state.isSetPwdDialog = false;
	});
};
const openPwdDialog = (row: any) => {
	userId.value = row.id;
	state.isSetPwdDialog = true;
};
// 暴露变量
defineExpose({
	openPwdDialog,
});
</script>
