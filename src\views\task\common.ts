import dayjs from 'dayjs';
// 根据日期查当前 周的所有日期
export function getDatesOfCurrentWeek(val: any) {
	const tabOps: any = [];
	const nowDay = dayjs(val).day();
	// 这周的第一天
	let str: any = null;
	if (nowDay) {
		// 非周天，即当周
		str = dayjs(val).startOf('week').add(1, 'day');
	} else {
		// 周天，先减去一天，当作是上周的，然后算出来是上周的周天是第一天，最后再加上一天就是周一
		str = dayjs(val).subtract(1, 'day').startOf('week').add(1, 'day');
	}
	for (let i = 0; i < 7; i++) {
		const date = dayjs(str).add(i, 'day').format('YYYY-MM-DD');
		tabOps.push(date);
	}
	return tabOps
}
// 计算当天的日期
export function getCurrentDate() {
	const date = new Date();
	const year = date.getFullYear();
	const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1，并且用padStart确保两位数
	const day = String(date.getDate()).padStart(2, '0'); // 用padStart确保两位数
	return `${year}-${month}-${day}`;
}
// 根据传入的周一时间去求上一周和下一周的所有日期
// 计算一周内所有日期的函数 startDate 周一日期  offsetWeeks 1获取下一周 -1 获取上一周
export function getWeekDates (startDate:string, offsetWeeks:number){
	const startOfWeek =dayjs(startDate)
	// 获取指定日期所在周的周一
	const weekDates = [];
	// 遍历一周的每一天
	for (let i = 0; i < 7; i++) {
		const date = startOfWeek.add(i + offsetWeeks * 7, 'day'); // 根据偏移量计算日期
		weekDates.push(date.format('YYYY-MM-DD'));
	}
	return weekDates;
}

// 获取前一天日期
export function getPreviousDay(date: string): string {
	return dayjs(date).subtract(1, 'day').format('YYYY-MM-DD');
}

