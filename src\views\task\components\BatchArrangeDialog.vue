<template>
	<el-dialog title="批量安排工作任务" v-model="dialogVisible" width="760px" :close-on-click-modal="false" @close="handleClose">
		<el-form :model="form" ref="formRef" :rules="rules" label-width="100px">
			<el-form-item label="选中人员">
				<div class="selected-users">
					<el-tag v-for="user in selectedUsers" :key="user.userId" class="mx-1">
						{{ user.userName }}
					</el-tag>
				</div>
			</el-form-item>
			<el-form-item label="考勤状态" prop="attendanceStatus">
				<el-radio-group v-model="form.attendanceStatus">
					<el-radio :label="2">正常出勤</el-radio>
					<el-radio :label="3">休假</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item label="时间段" prop="timeSlot">
				<el-select v-model="form.timeSlot" style="width: 100%" placeholder="请选择时间段">
					<el-option label="全天" value="10:00-18:00" />
					<el-option label="上午" value="10:00-12:00" />
					<el-option label="下午" value="14:00-18:00" />
					<el-option label="晚上(加班/18:00-22:00)" value="18:00-22:00" :disabled="form.attendanceStatus == 3" />
				</el-select>
			</el-form-item>
			<el-form-item label="反馈人" prop="leader" v-if="form.attendanceStatus == 2">
				<el-select v-model="form.leader" style="width: 100%" placeholder="请选择反馈人">
					<el-option v-for="item in leaderOptions" :key="item.id" :label="item.real_name" :value="item.id" />
				</el-select>
			</el-form-item>
			<el-form-item label="工作内容" prop="content" v-if="form.attendanceStatus == 2">
				<el-input v-model="form.content" type="textarea" :rows="4" placeholder="请输入工作内容" />
			</el-form-item>
			<el-form-item label="附件链接" prop="url" v-if="form.attendanceStatus == 2" class="form-link-item">
				<el-input v-model="form.url" placeholder="请输入链接地址" style="width: 49%"> </el-input>
				<el-input v-model="form.urlText" placeholder="请输入链接文本" style="width: 49%"> </el-input>
			</el-form-item>
		</el-form>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="dialogVisible = false">取消</el-button>
				<el-button type="primary" @click="handleSubmit">确定</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { ref, PropType } from 'vue';
import type { FormInstance } from 'element-plus';
import { ElMessage } from 'element-plus';
import { useUserApi } from '/@/api/user/index';
import { storeToRefs } from 'pinia';
import { useTaskApi } from '/@/api/task/index';

import { useUserInfo } from '/@/stores/userInfo';
const taskApi = useTaskApi();
const stores = useUserInfo();
const { userInfos } = storeToRefs(stores);
interface User {
	userId: number | string;
	userName: string;
}

const props = defineProps({
	selectedUsers: {
		type: Array as PropType<User[]>,
		required: true,
	},
});

const emit = defineEmits(['refresh', 'update:modelValue']);

const dialogVisible = ref(false);
const formRef = ref<FormInstance>();
const userApi = useUserApi();
const leaderOptions: any = ref([]);

const form: any = ref({
	timeSlot: '10:00-18:00',
	leader: userInfos.value.company === '腾讯' ? userInfos.value.userId : '',
	content: '',
	attendanceStatus: 2,
	url: '', // 新增附件链接字段
	urlText: '', // 新增附件链接文本字段
});

const rules = {
	timeSlot: [{ required: true, message: '请选择时间段', trigger: 'change' }],
	leader: [{ required: true, message: '请选择反馈人', trigger: 'change' }],
	content: [
		{
			validator: (rule: any, value: string, callback: Function) => {
				if (form.value.attendanceStatus !== 2) {
					callback();
					return;
				}

				const hasContent = !!value;
				const hasUrlText = !!form.value.urlText;
				const hasUrl = !!form.value.url;

				if (!hasContent && !hasUrlText && !hasUrl) {
					callback(new Error('请填写工作内容或链接信息'));
					return;
				}

				if (!hasContent && !hasUrlText && hasUrl) {
					callback(new Error('请填写工作内容或链接文本'));
					return;
				}

				if (!hasContent && hasUrlText && !hasUrl) {
					callback(new Error('请填写工作内容或链接地址'));
					return;
				}

				callback();
			},
			trigger: ['blur', 'change'],
		},
	],
	url: [
		{
			validator: (rule: any, value: string, callback: Function) => {
				if (form.value.attendanceStatus !== 2) {
					callback();
					return;
				}

				const hasUrlText = !!form.value.urlText;
				if (value && !hasUrlText && !form.value.content) {
					callback(new Error('请填写链接文本或工作内容'));
					return;
				}

				callback();
			},
			trigger: ['blur', 'change'],
		},
	],
	urlText: [
		{
			validator: (rule: any, value: string, callback: Function) => {
				if (form.value.attendanceStatus !== 2) {
					callback();
					return;
				}

				const hasUrl = !!form.value.url;
				if (value && !hasUrl && !form.value.content) {
					callback(new Error('请填写链接地址或工作内容'));
					return;
				}

				callback();
			},
			trigger: ['blur', 'change'],
		},
	],
};

// 获取反馈人列表
const getLeaderList = async () => {
	try {
		const { code, data } = await userApi.getUserList({ page: 1, page_size: 1000, is_leader: 1 });
		if (code === 200) {
			leaderOptions.value = data;
		}
	} catch (error) {
		//   console.error('获取反馈人列表失败:', error);
		ElMessage.error('获取反馈人列表失败');
	}
};

const handleSubmit = async () => {
	if (!formRef.value) return;
	await formRef.value.validate((valid) => {
		if (valid) {
			// 在提交前清理无效的链接信息
			const formData = {
				...form.value,
				users: props.selectedUsers,
			};

			// 如果只填写了链接地址没有文本，或只填写了文本没有地址，则清空链接相关信息
			// if ((!formData.url && formData.urlText) || (formData.url && !formData.urlText)) {
			// 	formData.url = '';
			// 	formData.urlText = '';
			// }

			handleBatchArrangeSuccess(formData);
		}
	});
};
// 批量安排成功处理
const loading = ref(false);
const handleBatchArrangeSuccess = async (formData: any) => {
	const params = {
		date: formData.users[0].date,
		user: formData.users.map((item: any) => item.userId),
		work_type: formData.users[0].workType,
		work: [
			{
				start_time: formData.timeSlot.split('-')[0],
				end_time: formData.timeSlot.split('-')[1],
				leader: formData.leader,
				detail: formData.attendanceStatus == 2 ? formData.content : '休假',
				status: formData.attendanceStatus,
				url: formData.url || '', // 添加附件链接字段
				url_text: formData.urlText || '', // 添加附件链接文本字段
			},
		],
	};
	try {
		// 这里调用批量安排的API
		loading.value = true;
		const res = await taskApi.addTaskWorkForTime(params);
		if (res.code === 200) {
			ElMessage.success('批量安排成功!');
			dialogVisible.value = false;
			emit('refresh');
		} else {
			ElMessage.error(res.message);
		}
	} catch (error: any) {
		ElMessage.error(error.response?.data?.message || '批量安排失败');
		// getTableData();
		// emit('refresh')
	} finally {
		loading.value = false;
	}
};
const handleClose = () => {
	formRef.value?.resetFields();
	dialogVisible.value = false;
};

// 暴露方法给父组件调用
defineExpose({
	show: () => {
		dialogVisible.value = true;
		getLeaderList();
	},
});
</script>

<style scoped lang="scss">
.selected-users {
	display: flex;
	flex-wrap: wrap;
	gap: 8px;
}
.dialog-footer {
	display: flex;
	justify-content: flex-end;
	gap: 12px;
}
.form-link-item {
	:deep(.el-form-item__content) {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}
}
</style>
