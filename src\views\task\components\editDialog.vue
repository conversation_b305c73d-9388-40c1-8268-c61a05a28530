<template>
	<div class="system-dic-dialog-container">
		<el-dialog
			:title="state.dialog.title"
			v-model="state.dialog.isShowDialog"
			:z-index="99"
			width="760px"
			append-to-body
			:close-on-click-modal="false"
		>
			<div class="tips">
				<span class="color999">已选时间段：</span>
				<span class="font times" style="color: #1890ff" v-for="(items, index) in tips" :key="index">{{ items }}，</span>
			</div>
			<el-form ref="editDialogFormRef" :model="state.ruleForm" :rules="rules" label-width="100px" :disabled="formDisabled">
				<el-form-item label="考勤状态:" prop="attendanceStatus" style="margin-bottom: 10px">
					<el-radio-group v-model="state.ruleForm.attendanceStatus" :disabled="editVal.isFeedback" @change="changeAttendanceStatus">
						<el-radio :label="2">正常出勤</el-radio>
						<el-radio :label="3">休假</el-radio>
					</el-radio-group>
				</el-form-item>
				<!-- 实际执行 显示进度和工作反馈人 -->
				<el-row v-if="state.ruleForm.attendanceStatus == 2 && detailsInfo.workType === '实际执行'" :gutter="20" style="margin-bottom: 20px">
					<el-col :span="12">
						<el-form-item label="工作进度" prop="progress">
							<el-select v-model="state.ruleForm.progress" placeholder="请选择工作进度" style="width: 100%" :disabled="editVal.isFeedback">
								<el-option v-for="item in progressList" :key="item.value" :label="item.label" :value="item.value" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="工作反馈人" prop="leader">
							<el-select
								v-model="state.ruleForm.leader"
								allow-create
								filterable
								placeholder="请选择工作反馈人"
								style="width: 100%"
								:disabled="editVal.isFeedback"
							>
								<el-option v-for="item in leaders" :key="item.value" :label="item.label" :value="item.value" />
							</el-select>
						</el-form-item>
					</el-col>
				</el-row>
				<!-- 原定安排 显示工作反馈人 -->
				<el-form-item label="工作反馈人:" prop="leader" v-if="state.ruleForm.attendanceStatus == 2 && detailsInfo.workType === '原定安排'">
					<el-select v-model="state.ruleForm.leader" placeholder="请选择工作反馈人" style="width: 100%" :disabled="editVal.isFeedback">
						<el-option v-for="item in leaders" :key="item.value" :label="item.label" :value="item.value" />
					</el-select>
				</el-form-item>
				<el-form-item v-if="state.ruleForm.attendanceStatus == 2" label="工作内容:" prop="details" :required="detailsInfo.workType === '实际执行'">
					<el-input :disabled="editVal.isFeedback" type="textarea" v-model="state.ruleForm.details" placeholder="工作内容" style="width: 100%">
					</el-input>
				</el-form-item>
				<el-form-item
					label="附件链接:"
					prop="url"
					class="form-link-item"
					v-if="state.ruleForm.attendanceStatus == 2 && detailsInfo.workType === '原定安排'"
				>
					<el-input :disabled="editVal.isFeedback" v-model="state.ruleForm.url" placeholder="请输入链接地址" style="width: 49%"> </el-input>
					<el-input :disabled="editVal.isFeedback" v-model="state.ruleForm.urlText" placeholder="请输入链接文本" style="width: 49%"> </el-input>
				</el-form-item>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="onCancel" size="default">取 消</el-button>
					<el-button v-if="!editVal.isFeedback" :disabled="formDisabled" :loading="loading" type="primary" @click="onSubmit" size="default">{{
						state.dialog.submitTxt
					}}</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts" name="taskEditDialog">
import { reactive, ref } from 'vue';
import { useUserApi } from '/@/api/user/index';
import { useTaskApi } from '/@/api/task/index';
import { ElMessage } from 'element-plus';
import { storeToRefs } from 'pinia';
import { useUserInfo } from '/@/stores/userInfo';
// 定义变量内容
const stores = useUserInfo();
const { userInfos } = storeToRefs(stores);
const userApi = useUserApi();
const taskApi = useTaskApi();
const formDisabled = ref(false);
// import {enumType} from '../common'
// 定义子组件向父组件传值/事件
const timeArr = ref([]);
const operationType: any = ref('');
const emit = defineEmits(['refresh']);
const leaders: any = ref([]);
const originLeaders: any = ref([]);
const progressList: any = ref([
	{ label: '进行中（25%）', value: '25%' },
	{ label: '进行中（50%）', value: '50%' },
	{ label: '进行中（75%）', value: '75%' },
	{ label: '已完成（100%）', value: '100%' },
]);
// 获取反馈人数据
const getUserData = async () => {
	const { code, data } = await userApi.getUserList({ page: 1, page_size: 100, is_leader: 1 });
	if (code === 200) {
		const arr: any = [];
		data.forEach((item: any) => {
			arr.push({ label: item.real_name, value: item.id });
		});
		leaders.value = arr; // 由于这个变量关联了el-select的options，需要修改
		originLeaders.value = arr; // 原始反馈人列表（不修改，只用于判断反馈人）
	} else {
		leaders.value = [];
	}
};
// 定义变量内容
const editDialogFormRef = ref();
const state: any = reactive({
	ruleForm: {
		leader: '',
		details: '',
		attendanceStatus: 2, // 默认选择正常出勤
		url: '',
		urlText: '', // 链接文本
		progress: '25%',
	},
	dialog: {
		isShowDialog: false,
		type: '',
		title: '安排工作内容',
		submitTxt: '确定',
	},
});
const tips = ref<string[]>([]);
let detailsInfo: any = ref({});
const editVal: any = ref({});
// 非空校验
const rules = reactive<any>({
	progress: [{ required: true, message: '请选择工作进度', trigger: 'change' }],
	leader: [{ required: true, message: '请选择反馈人!', trigger: 'change' }],
	details: [
		{
			validator: (rule: any, value: string, callback: any) => {
				// 只在原定安排时进行验证
				if (detailsInfo.value.workType === '原定安排') {
					const hasDetails = !!value;
					const hasUrl = !!state.ruleForm.url;
					const hasUrlText = !!state.ruleForm.urlText;

					// 情况1：有工作内容，链接和文本都可以为空
					if (hasDetails) {
						callback();
						return;
					}

					// 情况2：同时有链接和链接文本，工作内容可以为空
					if (hasUrl && hasUrlText) {
						callback();
						return;
					}

					// 情况3和4：只有链接地址或只有链接文本
					if ((hasUrl && !hasUrlText) || (!hasUrl && hasUrlText)) {
						callback(new Error('请填写完整的链接信息'));
						return;
					}

					// 都为空的情况
					if (!hasDetails && !hasUrl && !hasUrlText) {
						callback(new Error('请填写工作内容或完整的链接信息'));
						return;
					}

					callback();
				} else {
					// 实际执行的情况
					if (!value) {
						callback(new Error('请输入工作内容'));
						return;
					}
					callback();
				}
			},
			trigger: ['blur', 'change'],
		},
	],
	url: [
		{
			validator: (rule: any, value: string, callback: any) => {
				// 只在原定安排时进行验证
				if (detailsInfo.value.workType === '原定安排') {
					const hasDetails = !!state.ruleForm.details;
					const hasUrl = !!value;
					const hasUrlText = !!state.ruleForm.urlText;

					// 如果有工作内容，url可以为空
					if (hasDetails) {
						callback();
						return;
					}

					// 如果没有工作内容，但有链接文本，则链接地址必填
					if (!hasDetails && hasUrlText && !hasUrl) {
						callback(new Error('请填写链接地址'));
						return;
					}

					callback();
				} else {
					callback();
				}
			},
			trigger: ['blur', 'change'],
		},
	],
	urlText: [
		{
			validator: (rule: any, value: string, callback: any) => {
				// 只在原定安排时进行验证
				if (detailsInfo.value.workType === '原定安排') {
					const hasDetails = !!state.ruleForm.details;
					const hasUrl = !!state.ruleForm.url;
					const hasUrlText = !!value;

					// 如果有工作内容，urlText可以为空
					if (hasDetails) {
						callback();
						return;
					}

					// 如果没有工作内容，但有链接地址，则链接文本必填
					if (!hasDetails && hasUrl && !hasUrlText) {
						callback(new Error('请填写链接文本'));
						return;
					}

					callback();
				} else {
					callback();
				}
			},
			trigger: ['blur', 'change'],
		},
	],
});
// 打开弹窗
const openDialog = (type?: any, val?: any, info?: any) => {
	getUserData();
	formDisabled.value = false;
	editVal.value = {};
	operationType.value = type;
	tips.value = [];
	state.ruleForm.leader = '';
	state.ruleForm.details = '';
	state.ruleForm.url = '';
	detailsInfo.value = JSON.parse(JSON.stringify(info));
	if (type === 'edit') {
		editVal.value = JSON.parse(JSON.stringify(val));
		tips.value = [val.allTime];
		state.ruleForm.leader = val.leader;
		// 如果反馈人是手动输入的，则leader为null,feedback_name为手动输入的反馈人，则需要手动赋值leaders
		if (!val.leader) {
			const curEditInfo = info.work.find((item: any) => item.id === val.workId);
			state.ruleForm.leader = curEditInfo.feedback_name;
			leaders.value = [{ label: curEditInfo.feedback_name, value: curEditInfo.feedback_name }];
		}
		state.ruleForm.details = val.details;
		state.ruleForm.attendanceStatus = val.status;
		state.ruleForm.url = val.url; // 添加 url 赋值
		state.ruleForm.urlText = val.urlText; // 添加 urlText 赋值
		const today = new Date().setHours(0, 0, 0, 0);
		const selectedDate = new Date(info.date).setHours(0, 0, 0, 0);
		if (selectedDate < today) {
			formDisabled.value = true;
		} else {
			// console.log('formDisabled.value', formDisabled.value);
			formDisabled.value = false;
		}
		// 原定安排编辑框直接禁用 只能查看 不能填写内容
		if (detailsInfo.value.workType === '原定安排') {
			formDisabled.value = true;
		}
	} else {
		detailsInfo.value = JSON.parse(JSON.stringify(info));
		timeArr.value = JSON.parse(JSON.stringify(val));
		// 腾讯公司默认选择自己
		if (userInfos.value.company === '腾讯') {
			state.ruleForm.leader = userInfos.value.userId;
		}
		setTimePeriod();
	}
	// console.log('state.ruleForm.leader-----------------', state.ruleForm.leader);

	// console.log('leaders.value-----------------', leaders.value);
	// console.log('originLeaders.value-----------------', originLeaders.value);
	state.dialog.isShowDialog = true;

};
const changeAttendanceStatus = (val: any) => {
	state.ruleForm.details = '';
};
// 设置时间段tip
const setTimePeriod = () => {
	timeArr.value.forEach((item: any) => {
		tips.value.push(item.startTime + '-' + item.endTime);
	});
};
// 关闭弹窗
const closeDialog = () => {
	state.dialog.isShowDialog = false;
};
// 取消
const onCancel = () => {
	closeDialog();
};
// 提交
const loading = ref(false);
const onSubmit = async () => {
	editDialogFormRef.value.validate(async (valid: any) => {
		if (valid) {
			loading.value = true;
			try {
				const leader = state.ruleForm.leader;
				const detail = state.ruleForm.attendanceStatus == 2 ? state.ruleForm.details : '休假';
				const attendanceStatus = state.ruleForm.attendanceStatus;
				const progress = state.ruleForm.progress;
				const url = state.ruleForm.url; // 获取 url
				const urlText = state.ruleForm.urlText; // 获取 urlText
				const params: any = {
					work_type: detailsInfo.value.workType,
					user: detailsInfo.value.userId,
					date: detailsInfo.value.date,
				};
				// 检查 leader 是否在 leaders 列表中
				const isLeaderInList = originLeaders.value.some((item: any) => item.value === leader);

				if (operationType.value === 'add') {
					const arr: any = [];
					timeArr.value.forEach((item: any) => {
						const workItem: any = {
							start_time: item.startTime,
							end_time: item.endTime,
							leader: isLeaderInList ? leader : null, // 如果反馈人是手动输入的，则leader为空
							detail,
							status: attendanceStatus,
							url,
							url_text: urlText,
							feedback_name: isLeaderInList ? null : leader, // 如果反馈人是手动输入的，则feedback_name为输入值
						};
						// 只在实际执行时添加 schedule 参数
						if (detailsInfo.value.workType === '实际执行') {
							workItem.schedule = progress;
						}
						arr.push(workItem);
					});
					params.work = [...arr];
					// 判断是否是第一次新增,如果不是掉另外的接口
					if (detailsInfo.value.taskId) {
						params.work.forEach((item: any) => {
							item.task = detailsInfo.value.taskId;
						});
						await taskApi.addTaskWorkForTimeTwo(params.work);
					} else {
						await taskApi.addTaskWorkForTime(params);
					}
					ElMessage.success('添加成功!');
				} else {
					const obj: any = {
						start_time: editVal.value.startTime,
						end_time: editVal.value.endTime,
						// leader,
						// feedback_name,
						detail,
						status: attendanceStatus,
						task: detailsInfo.value.taskId,
						url,
						url_text: urlText,
						schedule: progress, // 进度
					};
					if (isLeaderInList) {
						obj.leader = leader;
					} else {
						obj.feedback_name = leader;
					}
					// 只在实际执行时添加 schedule 参数
					if (detailsInfo.value.workType === '实际执行') {
						obj.schedule = progress;
					}
					await taskApi.editTaskWorkForTime(obj, editVal.value.workId);
					ElMessage.success('修改成功!');
				}
				closeDialog();
				emit('refresh');
			} catch (error) {
				ElMessage.error('修改失败!');
			} finally {
				loading.value = false;
			}
		} else {
			ElMessage.error('请完善表单内容!');
		}
	});
};

// 暴露变量
defineExpose({
	openDialog,
});
</script>
<style lang="scss" scoped>
.tips {
	border-radius: 4px;
	line-height: 30px;
	border: 1px solid #1890ff;
	background: rgba(53, 158, 255, 0.05);
	padding: 5px 20px;
	margin-bottom: 25px;
	.times {
		margin-right: 0;
	}
}
.form-link-item {
	:deep(.el-form-item__content) {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}
}
</style>
