<template>
	<div>
		<div class="VueTimeSlot">
			<div class="VueTimeSlotList mt10 mb10" ref="currentRef">
				<template v-for="(item, index) in hoursData" :key="index">
					<div
						class="VueTimeSlotItems"
						v-if="![24, 25, 26, 27].includes(item.subscript)"
						:style="{ width: calculativeWidth(item) + 'px', ...setStyle(item.subscript) }"
						:class="compClass(item.subscript)"
						@click="handleClick(item.subscript)"
						@mouseover="handleHover(item.subscript)"
						@contextmenu.prevent="openMenu($event, item)"
						v-click-outside="onClickOutsideEl"
					>
						<div class="item-content-box" :class="props.detailsInfo.workType == '实际执行' ? 'done' : ''">
							<span class="details-text" :class="props.detailsInfo.workType == '原定安排' ? 'halfwidth' : ''">
								{{ getWorkDetails(item.subscript)?.details }}
							</span>
							<el-link
								v-if="showLinkIcon(getWorkDetails(item.subscript))"
								:href="formatUrl(getWorkDetails(item.subscript)?.url)"
								target="_blank"
								:underline="false"
								class="link-icon"
								@click.stop="handleLinkClick($event, getWorkDetails(item.subscript)?.url)"
							>
								<el-icon><Link /></el-icon>
								<span class="link-text">{{ getWorkDetails(item.subscript)?.urlText || '附件' }}</span>
							</el-link>
							<i
								class="leaderColor"
								v-if="
									getWorkDetails(item.subscript)?.workType == '原定安排' &&
									getWorkDetails(item.subscript)?.index !== 27 &&
									getWorkDetails(item.subscript)?.leaderName
								"
							>
								@{{ getWorkDetails(item.subscript)?.leaderName }}</i
							>
						</div>
						<!-- 显示进度 -->
						<span
							v-if="
								props.detailsInfo.workType == '实际执行' &&
								notOptionalIndex.includes(item.subscript) &&
								getWorkDetails(item.subscript)?.details != '休假'
							"
							class="process-text"
							>{{ getWorkDetails(item.subscript)?.schedule }}</span
						>
					</div>
					<Teleport to="body">
						<ul
							v-show="menuVisibel"
							:style="{ left: position.left + 'px', top: position.top + 'px', display: menuVisibel ? 'block' : 'none' }"
							class="contextmenu"
						>
							<div v-if="props.detailsInfo.workType == '实际执行' && rightClickItem.status == 2">
								<div class="item" v-if="!rightClickItem.is_feedback" @click="feedbackClick">反馈</div>
								<div class="item" @click="feedbackClick" v-if="rightClickItem.is_feedback">取消反馈</div>
							</div>
							<div class="item" @click="cancelClick" v-if="(!rightClickItem.is_feedback && rightClickItem.status == 2) || rightClickItem.status == 3">
								取消任务
							</div>
						</ul>
					</Teleport>
				</template>
			</div>
			<!-- 筛选项为原定安排或全部时，显示时间标签 -->
			<!-- <div class="timeLabel" v-if="detailsInfo.workType != '实际执行'">
				<template v-for="(item, index) in initTime" :key="index">
					<div class="timeLabelItems" v-if="!['12:00', '12:30', '13:00', '13:30'].includes(item)">
						<span :style="{ marginLeft: item.split(':')[0] == 14 ? -17 + 'px' : -7 + 'px' }">
							{{ index % 2 == 0 ? (item.split(':')[0] == 14 ? '12/14' : item.split(':')[0]) : '' }}</span
						>
					</div>
				</template>
			</div>
			<div class="timeLabel" v-if="filterWorkType == '实际执行' && detailsInfo.workType == '实际执行'">
				<template v-for="(item, index) in initTime" :key="index">
					<div class="timeLabelItems" v-if="!['12:00', '12:30', '13:00', '13:30'].includes(item)">
						<span :style="{ marginLeft: item.split(':')[0] == 14 ? -17 + 'px' : -7 + 'px' }">
							{{ index % 2 == 0 ? (item.split(':')[0] == 14 ? '12/14' : item.split(':')[0]) : '' }}</span
						>
					</div>
				</template>
			</div> -->
		</div>
		<EditDialog ref="editDialogRef" @refresh="updateTable"></EditDialog>
	</div>
</template>

<script setup lang="ts">
import { onMounted, ref, computed, watch, nextTick, defineAsyncComponent } from 'vue';
import { onClickOutside } from '@vueuse/core';
import { ClickOutside as vClickOutside } from 'element-plus';
// 引入 Link 图标
import { Link } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useTaskApi } from '/@/api/task';
const taskApi = useTaskApi();
const EditDialog = defineAsyncComponent(() => import('./editDialog.vue'));
const editDialogRef = ref();
const emit = defineEmits(['refresh']);
const props = defineProps({
	detailsInfo: {
		type: Object,
		default: () => {},
	},
	filterWorkType: {
		type: String,
		default: '',
	},
});
const hours = [10, 11, 12, 13, 14, 15, 16, 17, 18];
const selectStart = ref(false); // 开始
const startIndex: any = ref('0'); // 开始下标
const timeRangeList: any = ref([]); // 选择的时间段
const timeRangeListIndex: any = ref([]); // 选中的下标
const tempRangeIndex: any = ref([]); // 预选下标
const endHour: any = ref(''); //选择结束时间
const endMin: any = ref(''); //选择结束分钟
const startHour: any = ref(''); //开始时间
const startMin: any = ref(''); //开始时间
const sendTimeList: any = ref([]);
const startArr: any = ref([]);
const endArr: any = ref([]);
const notOptionalIndex: any = ref([]);
const workArr: any = ref([]);
const hoursData: any = ref([]);
// const isSelectProcess = ref(false);
watch(
	() => props.detailsInfo,
	() => {
		hoursData.value = JSON.parse(JSON.stringify(props.detailsInfo.work));
		sendTimeList.value = props.detailsInfo.work;
		nextTick(() => {
			meetingTime();
			transformedIndex(); // 数据变化更新表格
		});
	},
	{
		deep: true,
		immediate: true,
	}
);
// 初始化时间
const initTime = computed(() => {
	const arr = [];
	for (let i = 10; i <= 18; i++) {
		let h = i < 10 ? `0${i}` : i;
		// if (i != 22&&![12,13].includes(i)) {
		// 	arr.push(`${h}:00`, `${h}:30`);
		// } else if(i==22) {
		// 	arr.push(`${h}:00`);
		// }
		if (i != 18) {
			arr.push(`${h}:00`, `${h}:30`);
		} else {
			arr.push(`${h}:00`);
		}
	}
	return arr;
});
// 检查时间空隙
const checkTime = (time: any, arr: any) => {
	return arr.some((item: any) => time >= item.start_time && time < item.end_time);
};
// 填充时间数据
const meetingTime = () => {
	initTime.value.map((item, index) => {
		if (!checkTime(item, hoursData.value) && item !== '18:00') {
			hoursData.value.push({
				start_time: item,
				end_time: initTime.value[index + 1],
				status: -1,
				label: item, // 判断盒子索引
				// width:calculativeWidth(item)+'px'
			});
		}
	});
	// 按照时间顺序排序
	hoursData.value.sort((prev: any, next: any) => {
		let pTime = parseInt(prev.start_time.split(':').join(''));
		let nTime = parseInt(next.start_time.split(':').join(''));
		return pTime - nTime;
	});
	const arr: any = [];
	let num = 19;
	hoursData.value.forEach((item: any) => {
		if (item.end_time <= '18:00') {
			// hoursData.value.splice(index,1)
			// console.log('item',item);
			num = num + calculativeNum(item);
			item.subscript = num;
			arr.push(item);
		}
	});
	hoursData.value = arr;
	// console.log('hoursData.value', hoursData.value);
};
// 计算格子宽度
const calculativeWidth = (item: any) => {
	let multiplier =
		(item.end_time.split(':')[0] - item.start_time.split(':')[0]) * 2 + (item.end_time.split(':')[1] - item.start_time.split(':')[1]) / 30;
	if (item.start_time == '10:00' && item.end_time == '18:00') {
		// console.log('multiplier',multiplier);
		return (multiplier - 4) * 80;
	}
	if (item.start_time < '12:00' && item.end_time > '14:00') {
		return (multiplier - 4) * 80;
	}
	return multiplier * 80;
};
const calculativeNum = (item: any) => {
	let multiplier =
		(item.end_time.split(':')[0] - item.start_time.split(':')[0]) * 2 + (item.end_time.split(':')[1] - item.start_time.split(':')[1]) / 30;
	return multiplier;
};
// 点击事件
const handleClick = (index: any) => {
	if (notOptionalIndex.value.indexOf(index) > -1) {
		const currentInfo = getWorkDetails(index);
		editDialogRef.value.openDialog('edit', currentInfo, props.detailsInfo);
		return;
	}
	if(props.detailsInfo.workType == '原定安排'){
		ElMessage.warning('请到任务排页面,进行任务分配!')
		return
	}
	if (selectStart.value) {
		// 双击取反
		if (index === startIndex.value) {
			if (timeRangeListIndex.value.indexOf(index) > -1) {
				timeRangeListIndex.value.splice(timeRangeListIndex.value.indexOf(index), 1);
			} else {
				timeRangeListIndex.value.push(startIndex.value);
			}
		} else if (index > startIndex.value) {
			// 选取数据--向右添加，向左取消
			while (index >= startIndex.value) {
				timeRangeListIndex.value.push(startIndex.value);
				startIndex.value++;
			}
			timeRangeListIndex.value = Array.from(new Set(timeRangeListIndex.value));
		} else {
			// 删除数据
			while (startIndex.value >= index) {
				if (timeRangeListIndex.value.indexOf(index) > -1) {
					timeRangeListIndex.value.splice(timeRangeListIndex.value.indexOf(index), 1);
				}
				index++;
			}
		}
		// console.log('timeRangeListIndex.value', timeRangeListIndex.value);

		notOptionalIndex.value.forEach((item: any) => {
			if (timeRangeListIndex.value.includes(item)) {
				timeRangeListIndex.value.splice(timeRangeListIndex.value.indexOf(item), 1);
			}
		});
		// console.log('timeRangeListIndex.value', timeRangeListIndex.value);
		startIndex.value = '';
		transformedSection();
		tempRangeIndex.value = [];
		if (timeRangeListIndex.value.length > 0) {
			// 先校验时间段是否符合标准
			if (!validateAllTimeSlots()&&props.detailsInfo.workType == '实际执行') {
				showStandardTimeSlotsDialog();
				return;
			}
			
			const today = new Date().setHours(0, 0, 0, 0);
			const selectedDate = new Date(props.detailsInfo.date).setHours(0, 0, 0, 0);
			if (selectedDate < today) {
				ElMessage.warning('当前任务已过时效期，无法进行操作');
				return;
			}
			editDialogRef.value.openDialog('add', timeRangeList.value, props.detailsInfo);
		}
	} else {
		startIndex.value = index;
	}
	selectStart.value = !selectStart.value;
};
// 预选区间
const handleHover = (index: any) => {
	// if (notOptionalIndex.value.indexOf(index) > -1) return;
	if (selectStart.value) {
		tempRangeIndex.value = [];
		// 选取数据--向右添加，向左取消
		if (index > startIndex.value) {
			while (index >= startIndex.value) {
				tempRangeIndex.value.push(index);
				index--;
			}
		} else {
			// 删除数据
			while (startIndex.value >= index) {
				tempRangeIndex.value.push(index);
				index++;
			}
		}
		// console.log('tempRangeIndex.value', tempRangeIndex.value);
	}
};
// 是否选中，计算className
const compClass = (index: any) => {
	// const arr =[24,25,26,27]
	// if(arr.includes(index)){
	// 	return
	// }
	if (index === startIndex.value) {
		return 'selected';
	}
	if (index >= startIndex.value) {
		if (tempRangeIndex.value.indexOf(index) > -1) {
			return 'selected';
		}
	} else {
		if (tempRangeIndex.value.indexOf(index) > -1) {
			return 'unSelected';
		}
	}
	return timeRangeListIndex.value.indexOf(index) > -1 ? 'selected' : '';
};
// 设置样式
const setStyle = (index: any) => {
	const obj = getWorkDetails(index);
	// console.log('obj--------', obj);
	if (notOptionalIndex.value.includes(index)) {
		// 中午休息时间段样式禁用
		// if ([24, 25, 26, 27].includes(index)) {
		// 	return {
		// 		cursor: 'not-allowed',
		// 		backgroundColor: '#e9e9eb',
		// 	};
		// }
		// status = 2 正常出勤
		if (obj.status == 2 && obj.workType == '原定安排') {
			let styleObj: any = {
				backgroundColor: '#ffe9e8',
				// borderTop: `2px solid ${obj.isFeedback ? '#98d7b6' : '#ffc9c7'}`,
				// borderBottom: `2px solid ${obj.isFeedback ? '#98d7b6' : '#ffc9c7'}`,
				border: `2px solid #ffc9c7`,
			};
			// if (startArr.value.includes(index)) {
			// 	styleObj.borderLeft = `2px solid ${obj.isFeedback ? '#98d7b6' : '#ffc9c7'}`;
			// }
			// if (endArr.value.includes(index)) {
			// 	styleObj.borderRight = `2px solid ${obj.isFeedback ? '#98d7b6' : '#ffc9c7'}`;
			// }
			return styleObj;
		}
		if (obj.status == 2 && obj.workType == '实际执行') {
			let styleObj: any = {
				backgroundColor: `${obj.isFeedback ? '#c3ead5' : '#ffeead'}`,
				// borderTop: '2px solid #ffc000',
				// borderBottom: '2px solid #ffc000',
				border: `2px solid ${obj.isFeedback ? '#98d7b6' : '#ffc000'}`,
			};
			// if (startArr.value.includes(index)) {
			// 	styleObj.borderLeft = '2px solid #ffc000';
			// }
			// if (endArr.value.includes(index)) {
			// 	styleObj.borderRight = '2px solid #ffc000';
			// }
			return styleObj;
		}
		if (obj.status == 3) {
			return {
				// cursor: 'not-allowed',
				backgroundColor: '#e9e9eb',
			};
		}
	}
};
const currentTime = ref({});
// 找对应的工作内容
const getWorkDetails = (val: any) => {
	const data = workArr.value.find((item: any) => item.index == val);
	currentTime.value = data;
	return data;
};
// 时间区间转换成下标区间
const transformedIndex = () => {
	// console.log('sendTimeList.value',sendTimeList.value);
	timeRangeListIndex.value = [];
	notOptionalIndex.value = [];
	timeRangeList.value = sendTimeList.value;
	const arr: any = [];
	startArr.value = [];
	endArr.value = [];
	timeRangeList.value.forEach((item: any) => {
		const { start_time, end_time } = item;
		if (start_time && end_time) {
			let [startHour, startMin] = start_time.split(':');
			let [endHour, endMin] = end_time.split(':');
			if (startHour && startMin && endHour && endMin) {
				let startNum, endNum;
				if (startMin === '00') {
					startNum = 2 * parseInt(startHour);
				} else {
					startNum = 2 * parseInt(startHour) + 1;
				}
				if (endMin === '00') {
					endNum = 2 * parseInt(endHour) - 1;
				} else {
					endNum = 2 * parseInt(endHour);
				}
				startArr.value.push(startNum);
				endArr.value.push(endNum);
				while (endNum >= startNum) {
					notOptionalIndex.value.push(startNum);
					arr.push({
						index: startNum,
						startTime: item.start_time,
						endTime: item.end_time,
						status: item.status,
						leader: item.leader,
						leaderName: item.leader_name,
						urlText: item.url_text,
						details: item.detail,
						schedule: item.schedule,
						workType: props.detailsInfo.workType,
						allTime: item.start_time + '-' + item.end_time,
						workId: item.id,
						isFeedback: item.is_feedback,
						url: item.url, // 自定义链接
					});
					startNum++;
				}
			} else {
				// this.$message.error('时间段格式不正确');
			}
		} else {
			// this.$message.error('没有拿到开始时间或结束时间或者时间段格式不对');
		}
	});
	// tips.value = timeRangeList.value && timeRangeList.value.length > 0 ? timeRangeList.value : '';
	// console.log('notOptionalIndex.value', notOptionalIndex.value);
	workArr.value = arr;
};
// 下标区间转时间区间
const transformedSection = () => {
	timeRangeList.value = [];
	// Array.from(new Set(timeRangeList.value))
	let startTime = '',
		endTime = '',
		len = hours.length;
	for (let index = hours[0] * 2; index < 50; index++) {
		if (timeRangeListIndex.value.indexOf(index) > -1) {
			// 如果有开始时间，直接确定结束时间
			if (startTime) {
				endHour.value = Math.floor((index + 1) / 2);
				//判断是否重复选择选择下标开始结束时间
				if (endHour.value === startHour.value) {
					let endTimeAll = 30 * (index % 2) === 30 ? '30' : 30 * (index % 2);
					endMin.value = (index + 1) % 2 === 0 ? '00' : endTimeAll;
					endTime = `${endHour.value < 10 ? '0' + endHour.value : endHour.value}:${endMin.value}`;
				} else {
					let endTimeAll = 30 * ((index + 1) % 2) === 30 ? '30' : 30 * ((index + 1) % 2);
					endMin.value = (index + 1) % 2 === 0 ? '00' : endTimeAll;
					endTime = `${endHour.value < 10 ? '0' + endHour.value : endHour.value}:${endMin.value}`;
				}
			} else {
				// 没有开始时间，确定当前点为开始时间
				startHour.value = Math.floor(index / 2);
				let startTimeAll = 30 * (index % 2) === 30 ? '30' : 30 * (index % 2);
				startMin.value = index % 2 === 0 ? '00' : startTimeAll;
				startTime = `${startHour.value < 10 ? '0' + startHour.value : startHour.value}:${startMin.value}`;
			}
			// 如果是最后一格，直接结束
			if (index === 2 * hours.length + 1) {
				endTime = `${Math.floor((index + 1) / 2)}:00`;
				timeRangeList.value.push({
					startTime: startTime ? startTime : '23:30',
					endTime: endTime,
				});
				startTime = '';
				endTime = '';
			}
		} else {
			// 若这个点不在选择区间，确定一个时间段
			if (startTime && endTime) {
				timeRangeList.value.push({
					startTime: startTime,
					endTime: endTime,
				});
				startTime = '';
				endTime = '';
			} else if (startTime && !endTime) {
				// 这里可能只选半个小时
				endHour.value = Math.floor(index / 2);
				//判断是否重复选择选择下标开始结束时间
				if (endHour.value === startHour.value) {
					let endTimeAll = 30 * (index % 2) === 30 ? '30' : 30 * (index % 2);
					endMin.value = index % 2 === 0 ? '00' : endTimeAll;
					endTime = `${endHour.value < 10 ? '0' + endHour.value : endHour.value}:${endMin.value}`;
					timeRangeList.value.push({
						startTime: startTime,
						endTime: endTime,
					});
					startTime = '';
					endTime = '';
				} else {
					let endTimeAll = 30 * ((index + 1) % 2) === 30 ? '30' : 30 * ((index + 1) % 2);
					endMin.value = index % 2 === 0 ? '00' : endTimeAll;
					endTime = `${endHour.value < 10 ? '0' + endHour.value : endHour.value}:${endMin.value}`;
					timeRangeList.value.push({
						startTime: startTime,
						endTime: endTime,
					});
					startTime = '';
					endTime = '';
				}
			}
		}
	}
};
onMounted(() => {});
// 更新父级表格
const updateTable = () => {
	emit('refresh');
};

// 标准时间段定义
const STANDARD_TIME_SLOTS = [
	{ start: '10:00', end: '12:00' }, // 上午
	{ start: '14:00', end: '16:00' }, // 下午前半段
	{ start: '16:00', end: '18:00' }, // 下午后半段
	{ start: '14:00', end: '18:00' }, // 下午全时段
	{ start: '18:00', end: '22:00' }, // 晚上加班
	{ start: '10:00', end: '18:00' }  // 全天
];

// 校验是否为标准时间段
const isStandardTimeSlot = (startTime: string, endTime: string) => {
	// 检查是否匹配任何标准时间段
	for (const slot of STANDARD_TIME_SLOTS) {
		if (startTime === slot.start && endTime === slot.end) {
			return true;
		}
	}
	return false;
};

// 校验所有选择的时间段
const validateAllTimeSlots = () => {
	// 先检查是否为单独的全天时间段（10:00-18:00）
	if (timeRangeList.value.length === 1) {
		const range = timeRangeList.value[0];
		if (range.startTime === '10:00' && range.endTime === '18:00') {
			return true;
		}
	}
	
	// 检查是否为全天组合（上午+下午全时段）
	const morningExists = timeRangeList.value.some((range: any) => 
		range.startTime === '10:00' && range.endTime === '12:00'
	);
	const afternoonExists = timeRangeList.value.some((range: any) => 
		range.startTime === '14:00' && range.endTime === '18:00'
	);
	
	// 如果是全天组合，且只有这两个时间段，则通过
	if (morningExists && afternoonExists && timeRangeList.value.length === 2) {
		return true;
	}
	
	// 否则检查每个时间段是否都是标准时间段
	for (const timeRange of timeRangeList.value) {
		const { startTime, endTime } = timeRange;
		if (startTime && endTime) {
			if (!isStandardTimeSlot(startTime, endTime)) {
				return false;
			}
		}
	}
	return true;
};

// 显示标准时间段提示
const showStandardTimeSlotsDialog = () => {
	ElMessageBox.alert(
		`标准工作时段如下：
• 全天 (10:00-18:00)
• 上午 (10:00-12:00)
• 下午前半段 (14:00-16:00)
• 下午后半段 (16:00-18:00)
• 下午全时段 (14:00-18:00)
• 晚上加班 (18:00-22:00)
请按照以上时间段填写实际工作内容。`,
		'标准时间段提示',
		{
			confirmButtonText: '知道了',
			type: 'warning'
		}
	);
};
const currentRef = ref();
onClickOutside(currentRef, () => {
	selectStart.value = false;
	startIndex.value = 0;
	timeRangeListIndex.value = [];
	tempRangeIndex.value = [];
});

// 鼠标右键菜单逻辑
const onClickOutsideEl = () => {
	menuVisibel.value = false;
};
const menuVisibel = ref(false);
const position = ref({
	top: 0,
	left: 0,
});
const rightClickItem: any = ref({}); // 记录右键点击时的当前值
const openMenu = (e: MouseEvent, item: any) => {
	if (!item.id) return;
	const today = new Date().setHours(0, 0, 0, 0);
	const selectedDate = new Date(props.detailsInfo.date).setHours(0, 0, 0, 0);
	if (selectedDate < today) {
		ElMessage.warning('当前任务已过时效期，无法进行操作');
		return;
	}
	menuVisibel.value = true;
	position.value.top = e.pageY;
	position.value.left = e.pageX;
	rightClickItem.value = item;
};
const closeMenu = () => {
	menuVisibel.value = false;
};
watch(menuVisibel, () => {
	if (menuVisibel.value) {
		// if (isSelectProcess.value) {
		// 	return;
		// }
		document.body.addEventListener('click', closeMenu);
	} else {
		document.body.removeEventListener('click', closeMenu);
	}
});

// 反馈
const feedbackClick = async () => {
	// if (!isSelectProcess.value) {
	// 	ElMessage.warning('请先选择进度');
	// 	return;
	// }
	// try {

	// } catch (error) {
	// 	ElMessage.error('操作失败');
	// 	console.error(error);
	// }

	const { id, is_feedback } = rightClickItem.value;
	const params = {
		is_feedback: !is_feedback,
	};
	await taskApi.taskFeedback(params, id);

	// 更新本地状态
	// rightClickItem.value.is_feedback = !is_feedback;

	ElMessage.success('操作成功!');
	emit('refresh');
};

// 取消任务
const cancelClick = async () => {
	const { id } = rightClickItem.value;
	await taskApi.delTaskForTime(id);
	ElMessage.success('取消成功!');
	emit('refresh');
};

// 判断是否显示链接图标
const showLinkIcon = (workDetail: any) => {
	return workDetail?.url && workDetail?.url.trim() !== '' && workDetail?.workType == '原定安排';
};

// 格式化URL，确保包含http://或https://
const formatUrl = (url: string) => {
	if (!url) return '';
	if (url.startsWith('http://') || url.startsWith('https://')) {
		return url;
	}
	return `https://${url}`;
};
// 处理链接点击事件
const handleLinkClick = (event: Event, url: string) => {
	event.preventDefault(); // 阻止默认事件
	event.stopPropagation(); // 阻止事件冒泡
	if (url) {
		window.open(formatUrl(url), '_blank');
	}
};
</script>

<style lang="scss" scoped>
.VueTimeSlot {
	width: 1080px;
	margin: 0 auto;
	// overflow: hidden;
	box-sizing: border-box;
	.link-icon {
		margin-left: 8px;
		font-size: 14px;
		color: #409eff;
		cursor: pointer;
		display: inline-flex;
		align-items: center;

		&:hover {
			color: #66b1ff;
		}

		.el-icon {
			// margin-right: 4px;
		}

		.link-text {
			font-size: 12px;
		}
	}
	.VueTimeSlotList {
		width: 100%;
		display: flex;
		border-radius: 3px;
		.VueTimeSlotItems {
			position: relative;
			box-sizing: border-box;
			display: inline-block;
			// display: flex;
			// justify-content: center;
			// align-items: center;
			// height: 30px;
			min-height: 30px;
			// padding: 10px 0px;
			color: #507387;
			border: 1px #dfd8e0 solid;
			cursor: pointer;
			font-size: 12px;
			// line-height: 30px;
			white-space: normal;
			// display: flex;
			// align-items: center;
			// justify-content: center;
			// margin: auto;
			align-content: center;
			// display: -webkit-box;
			// -webkit-line-clamp: 2;
			// -webkit-box-orient: vertical;
			// overflow: hidden;
			// text-overflow: ellipsis;
			// span {
			// 	display: block;
			// 	width: 100%;
			// 	overflow: hidden;
			// 	text-overflow: ellipsis;
			// 	white-space: nowrap;
			// }
			.item-content-box {
				display: flex;
				align-items: center;
				display: block;
				// width: 100%;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				&.done {
					width: calc(100% - 40px);
				}
			}
			.details-text.halfwidth {
				// flex-shrink: 0;
				display: inline-block;
				max-width: 50%;
				vertical-align: middle;
				// display: block;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
			.process-text {
				width: auto;
				display: inline;
				color: #3c9bf4;
				position: absolute;
				top: 7px;
				right: 10px;
			}
			&:last-child {
				border-right: 1px #dfd8e0 solid;
				.VueTimeSlotBox {
					border-right: 1px #dfd8e0 solid;
				}
				// display: none;
			}
			.VueTimeSlotBoxSelect {
				background-color: #4f9bfa !important;
			}
			.VueTimeSlotBoxItems {
				background-color: rgba(50, 150, 250, 0.3);
			}
		}
		.leaderColor {
			color: #409eff;
		}
		.selected {
			background-color: rgba(0, 87, 255, 0.4);
		}
		.hiddenCell {
			display: none;
		}
	}
	.timeLabel {
		width: 100%;
		display: flex;
		height: 16px;
		.timeLabelItems {
			width: 80px;
			height: 16px;
			text-align: left;
		}
	}
	.mt10 {
		margin-top: 5px;
	}
	.mb10 {
		margin-bottom: 5px;
	}
}
.contextmenu {
	width: 100px;
	margin: 0;
	background: #fff;
	z-index: 3000;
	//   position: absolute;
	position: fixed;
	list-style-type: none;
	padding: 5px 0;
	border-radius: 4px;
	font-size: 12px;
	font-weight: 400;
	color: #333;
	box-shadow: 3px 3px 4px 0 rgba(0, 0, 0, 0.1);

	.item {
		padding: 0 15px;
		height: 35px;
		width: 100%;
		line-height: 35px;
		color: rgb(29, 33, 41);
		cursor: pointer;
		&.fb {
			display: flex;
			align-items: center;
			.fb-progress-popper {
				width: 100%;
				bottom: 20px;
				margin-bottom: 20px;
			}
			span {
				flex-shrink: 0;
			}
			.fb-progress-text {
				color: #bca304;
			}
		}
	}
	.item:hover {
		background: rgb(229, 230, 235);
	}
}
</style>
