<template>
	<div class="meeting-time-range" ref="currentRef">
		<div class="hours-container">
			<div v-for="(item, index) in hours" :key="index" class="hours-item">
				<div class="hours-item-value">
					<el-popover placement="top" :width="300" trigger="hover" effect="dark" v-if="getWorkDetails(2 * item)?.status == 2">
						<template #reference>
							<div :class="compClass(2 * item)" @click="handleClick(2 * item)" @mouseover="handleHover(2 * item)" :style="setStyle(2 * item)"></div>
						</template>
						<div class="workDetails">
							<div class="work_time">
								<span class="label">工作日期:</span><span>{{ props.detailsInfo.date }}</span>
							</div>
							<div class="work_time">
								<span class="label">时间段:</span><span>{{ getWorkDetails(2 * item)?.startTime + '-' + getWorkDetails(2 * item)?.endTime }}</span>
							</div>
							<div class="work_time">
								<span class="label">工作反馈人:</span><span>{{ getWorkDetails(2 * item)?.leaderName }}</span>
							</div>
							<div class="work_time">
								<span class="label">工作内容:</span><span>{{ getWorkDetails(2 * item)?.details }}</span>
							</div>
							<div class="work_time">
								<span class="label">操作:</span>
								<template v-if="getWorkDetails(2 * item)?.workType == '原定安排'">
									<el-button v-if="!getWorkDetails(2 * item)?.isFeedback" :type="'success'" @click="feedbackClick(2 * item)" link>反馈</el-button>
									<el-button v-if="getWorkDetails(2 * item)?.isFeedback" :type="'info'" @click="feedbackClick(2 * item)" link>取消反馈</el-button>
								</template>
								<el-button v-if="!getWorkDetails(2 * item)?.isFeedback" :type="'danger'" @click="cancelClick(2 * item)" link>取消任务</el-button>
							</div>
						</div>
					</el-popover>
					<div
						v-else
						:class="compClass(2 * item)"
						@click="handleClick(2 * item)"
						@mouseover="handleHover(2 * item)"
						:style="setStyle(2 * item)"
					></div>
					<el-popover placement="top" :width="300" trigger="hover" effect="dark" v-if="getWorkDetails(2 * item + 1)?.status == 2">
						<template #reference>
							<div
								:class="compClass(2 * item + 1)"
								@click="handleClick(2 * item + 1)"
								@mouseover="handleHover(2 * item + 1)"
								:style="setStyle(2 * item + 1)"
							></div>
						</template>
						<div class="workDetails">
							<div class="work_time">
								<span class="label">工作日期:</span><span>{{ props.detailsInfo.date }}</span>
							</div>
							<div class="work_time">
								<span class="label">时间段:</span
								><span>{{ getWorkDetails(2 * item + 1)?.startTime + '-' + getWorkDetails(2 * item + 1)?.endTime }}</span>
							</div>
							<div class="work_time">
								<span class="label">工作反馈人:</span><span>{{ getWorkDetails(2 * item + 1)?.leaderName }}</span>
							</div>
							<div class="work_time">
								<span class="label">工作内容:</span><span>{{ getWorkDetails(2 * item + 1)?.details }}</span>
							</div>
							<div class="work_time">
								<span class="label">操作:</span>
								<template v-if="getWorkDetails(2 * item + 1)?.workType == '原定安排'">
									<el-button v-if="!getWorkDetails(2 * item + 1)?.isFeedback" :type="'success'" @click="feedbackClick(2 * item+1)" link>反馈</el-button>
									<el-button v-if="getWorkDetails(2 * item + 1)?.isFeedback" :type="'info'" @click="feedbackClick(2 * item+1)" link>取消反馈</el-button>
								</template>
								<el-button v-if="!getWorkDetails(2 * item + 1)?.isFeedback" :type="'danger'" @click="cancelClick(2 * item + 1)" link
									>取消任务</el-button
								>
							</div>
						</div>
					</el-popover>
					<div
						v-else
						:class="compClass(2 * item + 1)"
						@click="handleClick(2 * item + 1)"
						@mouseover="handleHover(2 * item + 1)"
						:style="setStyle(2 * item + 1)"
					></div>
				</div>
				<div class="hours-item-header">
					<span>{{ timeHours[index] }}</span>
				</div>
			</div>
		</div>
		<EditDialog ref="editDialogRef" @refresh="updateTable"></EditDialog>
	</div>
</template>
<script setup lang="ts" name="taskTime">
import { onMounted, ref, defineAsyncComponent, watch, nextTick } from 'vue';
import { onClickOutside } from '@vueuse/core';
import { ElMessage } from 'element-plus';
import { useTaskApi } from '/@/api/task';
const taskApi = useTaskApi();
const emit = defineEmits(['refresh']);
const props = defineProps({
	detailsInfo: {
		type: Object,
		default: () => {},
	},
});
const EditDialog = defineAsyncComponent(() => import('./editDialog.vue'));
const editDialogRef = ref();
const hours = [10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22];
const timeHours = ['10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00', '19:00', '20:00', '21:00', '22:00']; // 选项
const selectStart = ref(false); // 开始
const startIndex: any = ref('0'); // 开始下标
const timeRangeList: any = ref([]); // 选择的时间段
const timeRangeListIndex: any = ref([]); // 选中的下标
const tempRangeIndex: any = ref([]); // 预选下标
const endHour: any = ref(''); //选择结束时间
const endMin: any = ref(''); //选择结束分钟
const startHour: any = ref(''); //开始时间
const startMin: any = ref(''); //开始时间
const sendTimeList: any = ref([]);
const startArr: any = ref([]);
const endArr: any = ref([]);
const notOptionalIndex: any = ref([]);
const workArr: any = ref([]);
watch(
	() => props.detailsInfo,
	() => {
		sendTimeList.value = props.detailsInfo.work;
		nextTick(() => {
			transformedIndex(); // 数据变化更新表格
		});
	},
	{
		deep: true,
		immediate: true,
	}
);
// 时间区间转换成下标区间
const transformedIndex = () => {
	// console.log('sendTimeList.value',sendTimeList.value);
	timeRangeListIndex.value = [];
	notOptionalIndex.value = [];
	timeRangeList.value = sendTimeList.value;
	const arr: any = [];
	startArr.value = [];
	endArr.value = [];
	timeRangeList.value.forEach((item: any) => {
		const { start_time, end_time } = item;
		if (start_time && end_time) {
			let [startHour, startMin] = start_time.split(':');
			let [endHour, endMin] = end_time.split(':');
			if (startHour && startMin && endHour && endMin) {
				let startNum, endNum;
				if (startMin === '00') {
					startNum = 2 * parseInt(startHour);
				} else {
					startNum = 2 * parseInt(startHour) + 1;
				}
				if (endMin === '00') {
					endNum = 2 * parseInt(endHour) - 1;
				} else {
					endNum = 2 * parseInt(endHour);
				}
				startArr.value.push(startNum);
				endArr.value.push(endNum);
				while (endNum >= startNum) {
					notOptionalIndex.value.push(startNum);
					arr.push({
						index: startNum,
						startTime: item.start_time,
						endTime: item.end_time,
						status: item.status,
						leader: item.leader,
						leaderName: item.leader_name,
						details: item.detail,
						workType: props.detailsInfo.workType,
						allTime: item.start_time + '-' + item.end_time,
						workId: item.id,
						isFeedback: item.is_feedback,
					});
					startNum++;
				}
			} else {
				// this.$message.error('时间段格式不正确');
			}
		} else {
			// this.$message.error('没有拿到开始时间或结束时间或者时间段格式不对');
		}
	});
	// tips.value = timeRangeList.value && timeRangeList.value.length > 0 ? timeRangeList.value : '';
	// console.log('notOptionalIndex.value', notOptionalIndex.value);
	workArr.value = arr;
};
// 下标区间转换成时间区间
const transformedSection = () => {
	timeRangeList.value = [];
	// Array.from(new Set(timeRangeList.value))
	let startTime = '',
		endTime = '',
		len = hours.length;
	for (let index = hours[0] * 2; index < 50; index++) {
		if (timeRangeListIndex.value.indexOf(index) > -1) {
			// 如果有开始时间，直接确定结束时间
			if (startTime) {
				endHour.value = Math.floor((index + 1) / 2);
				//判断是否重复选择选择下标开始结束时间
				if (endHour.value === startHour.value) {
					let endTimeAll = 30 * (index % 2) === 30 ? '30' : 30 * (index % 2);
					endMin.value = (index + 1) % 2 === 0 ? '00' : endTimeAll;
					endTime = `${endHour.value < 10 ? '0' + endHour.value : endHour.value}:${endMin.value}`;
				} else {
					let endTimeAll = 30 * ((index + 1) % 2) === 30 ? '30' : 30 * ((index + 1) % 2);
					endMin.value = (index + 1) % 2 === 0 ? '00' : endTimeAll;
					endTime = `${endHour.value < 10 ? '0' + endHour.value : endHour.value}:${endMin.value}`;
				}
			} else {
				// 没有开始时间，确定当前点为开始时间
				startHour.value = Math.floor(index / 2);
				let startTimeAll = 30 * (index % 2) === 30 ? '30' : 30 * (index % 2);
				startMin.value = index % 2 === 0 ? '00' : startTimeAll;
				startTime = `${startHour.value < 10 ? '0' + startHour.value : startHour.value}:${startMin.value}`;
			}
			// 如果是最后一格，直接结束
			if (index === 2 * hours.length + 1) {
				endTime = `${Math.floor((index + 1) / 2)}:00`;
				timeRangeList.value.push({
					startTime: startTime ? startTime : '23:30',
					endTime: endTime,
				});
				startTime = '';
				endTime = '';
			}
		} else {
			// 若这个点不在选择区间，确定一个时间段
			if (startTime && endTime) {
				timeRangeList.value.push({
					startTime: startTime,
					endTime: endTime,
				});
				startTime = '';
				endTime = '';
			} else if (startTime && !endTime) {
				// 这里可能只选半个小时
				endHour.value = Math.floor(index / 2);
				//判断是否重复选择选择下标开始结束时间
				if (endHour.value === startHour.value) {
					let endTimeAll = 30 * (index % 2) === 30 ? '30' : 30 * (index % 2);
					endMin.value = index % 2 === 0 ? '00' : endTimeAll;
					endTime = `${endHour.value < 10 ? '0' + endHour.value : endHour.value}:${endMin.value}`;
					timeRangeList.value.push({
						startTime: startTime,
						endTime: endTime,
					});
					startTime = '';
					endTime = '';
				} else {
					let endTimeAll = 30 * ((index + 1) % 2) === 30 ? '30' : 30 * ((index + 1) % 2);
					endMin.value = index % 2 === 0 ? '00' : endTimeAll;
					endTime = `${endHour.value < 10 ? '0' + endHour.value : endHour.value}:${endMin.value}`;
					timeRangeList.value.push({
						startTime: startTime,
						endTime: endTime,
					});
					startTime = '';
					endTime = '';
				}
			}
		}
	}
	// console.log('timeRangeList.value', timeRangeList.value);

	// tips.value = timeRangeList.value && timeRangeList.value.length > 0 ? timeRangeList.value : '';
};
// 点击事件
const handleClick = (index: any) => {
	if (notOptionalIndex.value.indexOf(index) > -1) {
		editDialogRef.value.openDialog('edit', currentTime.value, props.detailsInfo);
		return;
	}

	if (selectStart.value) {
		// 双击取反
		if (index === startIndex.value) {
			if (timeRangeListIndex.value.indexOf(index) > -1) {
				timeRangeListIndex.value.splice(timeRangeListIndex.value.indexOf(index), 1);
			} else {
				timeRangeListIndex.value.push(startIndex.value);
			}
		} else if (index > startIndex.value) {
			// 选取数据--向右添加，向左取消
			while (index >= startIndex.value) {
				timeRangeListIndex.value.push(startIndex.value);
				startIndex.value++;
			}
			timeRangeListIndex.value = Array.from(new Set(timeRangeListIndex.value));
		} else {
			// 删除数据
			while (startIndex.value >= index) {
				if (timeRangeListIndex.value.indexOf(index) > -1) {
					timeRangeListIndex.value.splice(timeRangeListIndex.value.indexOf(index), 1);
				}
				index++;
			}
		}
		notOptionalIndex.value.forEach((item: any) => {
			if (timeRangeListIndex.value.includes(item)) {
				timeRangeListIndex.value.splice(timeRangeListIndex.value.indexOf(item), 1);
			}
		});
		// console.log('timeRangeListIndex.value', timeRangeListIndex.value);
		startIndex.value = '';
		transformedSection();
		tempRangeIndex.value = [];
		if (timeRangeListIndex.value.length > 0) {
			editDialogRef.value.openDialog('add', timeRangeList.value, props.detailsInfo);
		}
	} else {
		startIndex.value = index;
	}
	selectStart.value = !selectStart.value;
};
// 预选区间
const handleHover = (index: any) => {
	if (notOptionalIndex.value.indexOf(index) > -1) return;
	if (selectStart.value) {
		tempRangeIndex.value = [];
		// 选取数据--向右添加，向左取消
		if (index > startIndex.value) {
			while (index >= startIndex.value) {
				tempRangeIndex.value.push(index);
				index--;
			}
		} else {
			// 删除数据
			while (startIndex.value >= index) {
				tempRangeIndex.value.push(index);
				index++;
			}
		}
		// console.log('tempRangeIndex.value', tempRangeIndex.value);
	}
};
// 是否选中，计算className
const compClass = (index: any) => {
	if (index === startIndex.value) {
		return 'hours-item-left selected';
	}
	if (index >= startIndex.value) {
		if (tempRangeIndex.value.indexOf(index) > -1) {
			return 'hours-item-left selected';
		}
	} else {
		if (tempRangeIndex.value.indexOf(index) > -1) {
			return 'hours-item-left unSelected';
		}
	}
	return timeRangeListIndex.value.indexOf(index) > -1 ? 'hours-item-left selected' : 'hours-item-left';
};
const currentTime = ref({});
// 找对应的工作内容
const getWorkDetails = (val: any) => {
	const data = workArr.value.find((item: any) => item.index == val);
	currentTime.value = data;
	return data;
};
// 取消任务
const cancelClick = async (index: any) => {
	const id = getWorkDetails(index).workId;
	await taskApi.delTaskForTime(id);
	ElMessage.success('取消成功!');
	emit('refresh');
};
// 反馈
const feedbackClick = async (index: any) => {
	const { workId, isFeedback } = getWorkDetails(index);
	const params = {
		is_feedback: !isFeedback,
	};
	await taskApi.taskFeedback(params, workId);
	ElMessage.success('操作成功!');
	emit('refresh');
};
// 设置样式
const setStyle = (index: any) => {
	const obj = getWorkDetails(index);
	if (notOptionalIndex.value.includes(index)) {
		// 中午休息时间段样式禁用
		if ([24, 25, 26, 27].includes(index)) {
			return {
				cursor: 'not-allowed',
				backgroundColor: '#e9e9eb',
			};
		}
		if (obj.status == 2 && obj.workType == '原定安排') {
			let styleObj: any = {
				backgroundColor: `${obj.isFeedback ? '#c3ead5' : '#ffe9e8'}`,
				borderTop: `2px solid ${obj.isFeedback ? '#98d7b6' : '#ffc9c7'}`,
				borderBottom: `2px solid ${obj.isFeedback ? '#98d7b6' : '#ffc9c7'}`,
			};
			if (startArr.value.includes(index)) {
				styleObj.borderLeft = `2px solid ${obj.isFeedback ? '#98d7b6' : '#ffc9c7'}`;
			}
			if (endArr.value.includes(index)) {
				styleObj.borderRight = `2px solid ${obj.isFeedback ? '#98d7b6' : '#ffc9c7'}`;
			}
			return styleObj;
		}
		if (obj.status == 2 && obj.workType == '实际执行') {
			let styleObj: any = {
				backgroundColor: '#ffeead',
				borderTop: '2px solid #ffc000',
				borderBottom: '2px solid #ffc000',
			};
			if (startArr.value.includes(index)) {
				styleObj.borderLeft = '2px solid #ffc000';
			}
			if (endArr.value.includes(index)) {
				styleObj.borderRight = '2px solid #ffc000';
			}
			return styleObj;
		}
	}
};
//
const currentRef = ref();
onClickOutside(currentRef, () => {
	selectStart.value = false;
	startIndex.value = 0;
	timeRangeListIndex.value = [];
	tempRangeIndex.value = [];
});
onMounted(() => {
	transformedIndex();
	transformedSection();
});
// 更新父级表格
const updateTable = () => {
	emit('refresh');
};
</script>
<style lang="scss" scoped>
.meeting-time-range {
	// padding: 15px;
}

.hours-container {
	margin-left: 1.5%;
	display: flex;
	cursor: pointer;
	margin-top: 10px;

	.hours-item {
		width: 10%;
		border-right: none;
		text-align: center;

		&:nth-last-of-type(2) {
			.hours-item-value {
				border-right: 1px solid #ccc;
			}
		}

		&:last-child {
			.hours-item-value {
				display: none;
			}

			.hours-item-header {
				margin-top: 30px;
			}
		}

		.hours-item-header {
			text-align: left;
			margin-left: -22%;
		}

		.hours-item-value {
			width: 100%;
			height: 31px;
			box-sizing: border-box;
			display: flex;
			border-top: 1px solid #ccc;
			border-bottom: 1px solid #ccc;
			border-left: 1px solid #ccc;

			.hours-item-left,
			.hours-item-right {
				width: 50%;
				height: 30px;
				box-sizing: border-box;
			}
			.hours-item-left {
				&:nth-child(2n + 1) {
					border-right: 1px solid #ccc;
				}
			}
		}

		.selected {
			background-color: rgba(0, 87, 255, 0.4);
		}

		.preSelected {
			background-color: rgba(255, 148, 49, 0.4);
		}

		.unSelected {
			background-color: #ffffff;
		}
		.disabled {
			cursor: not-allowed !important;
			// backgroundcolor: '#e9e9eb',
			background-color: #e9e9eb !important;
			// border-top: 2px solid #969697;
			// border-bottom: 2px solid #969697;
		}
		.original {
			background-color: #ffe9e8 !important;
		}
		.actual {
			background-color: #ffeead !important;
		}
	}
}

.tips {
	border-radius: 4px;
	line-height: 30px;
	border: 1px solid #1890ff;
	background: rgba(53, 158, 255, 0.05);
	padding: 5px 20px;

	.times {
		margin-right: 0;
	}
}
.workDetails {
	// text-align: center;
}
</style>
