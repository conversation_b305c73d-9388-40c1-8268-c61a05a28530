<template>
	<div class="system-role-container layout-padding">
		<div class="system-role-padding layout-padding-auto layout-padding-view pd15">
			<div class="system-user-search">
				<el-form :inline="true" :model="formInline" size="default">
					<div class="form-item-h space-between">
						<div class="left">
							<el-form-item label="业务分类">
								<el-select v-model="formInline.group" placeholder="业务分类" clearable @change="handleSearch" style="width: 460px">
									<el-option v-for="item in groupEnum" :key="item.value" :label="item.label" :value="item.value">
										{{ item.label }}
									</el-option>
								</el-select>
							</el-form-item>
							<el-form-item label="人员选择">
								<el-cascader
									v-model="formInline.ids"
									:options="cascaderOptions"
									:props="cascaderProps"
									placeholder="请选择人员"
									clearable
									filterable
									collapse-tags
									:max-collapse-tags="4"
									@change="handleSearch"
									style="width: 460px"
								/>
							</el-form-item>
						</div>
					</div>
					<div class="form-item-h">
						<el-form-item label="展示内容">
							<el-select v-model="formInline.work_type" placeholder="展示内容" clearable @change="handleSearch" style="width: 460px">
								<el-option v-for="item in workTypeEnum" :key="item.value" :label="item.label" :value="item.value" />
							</el-select>
						</el-form-item>
						<el-form-item label="任务状态">
							<el-select v-model="formInline.is_arrange" placeholder="任务状态" clearable @change="handleSearch" style="width: 460px">
								<el-option v-for="item in arrangeStatusEnum" :key="item.value" :label="item.label" :value="item.value" />
							</el-select>
						</el-form-item>
					</div>
					<el-form-item label="日期查询">
						<el-date-picker
							v-model="formInline.searchDate"
							value-format="YYYY-MM-DD"
							type="date"
							placeholder="请选择日期"
							style="width: 460px"
							clearable
							@change="handleDateChange"
						></el-date-picker>
						<el-button-group style="margin-left: 195px">
							<el-button class="group-btn" type="primary" @click="optionWeekClick(-1)"
								><el-icon class="el-icon--right"><ArrowLeft /></el-icon>上周</el-button
							>
							<el-button type="success" class="reset-btn group-btn" @click="initializeTime">重置</el-button>
							<el-button type="primary" class="group-btn" @click="optionWeekClick(1)">
								下周<el-icon class="el-icon--right"><ArrowRight /></el-icon>
							</el-button>
						</el-button-group>
					</el-form-item>
				</el-form>
				<div class="info">
					<div class="mark">
						<div class="mark-item" v-for="(item, index) in markValue" :key="index">
							<span class="mark-item-icon" :style="{ backgroundColor: item.color }"></span>
							<span class="mark-item-lebel">{{ item.label }}</span>
						</div>
					</div>
				</div>
			</div>
			<el-tabs type="card" v-model="tabActive" min-height="400px" @tab-change="handleClickDate">
				<el-tab-pane
					v-for="day in weeklyDates"
					:key="day.date"
					:label="tabActive === day.date ? day.weekly + ' （' + day.abbr + '）' : day.weekly"
					:name="day.date"
				></el-tab-pane>
			</el-tabs>
			<div style="padding-bottom: 10px; border-left: 1px solid #e4e7ed" v-if="formInline.work_type === '原定安排'">
				<el-button size="default" type="primary" class="ml10" @click="handleBatchArrange" :disabled="!multipleSelection.length">
					<el-icon>
						<ele-FolderAdd />
					</el-icon>
					批量安排工作任务
				</el-button>
			</div>
			<el-table
				:data="tableData"
				v-loading="loading"
				ref="tableRef"
				border
				:header-cell-style="spanHeader2"
				@selection-change="handleSelectionChange"
				:span-method="spanMethod2"
				>

				<!-- <el-table-column
					v-if="formInline.work_type == '原定安排'"
					type="selection"
					width="55"
					align="center"
					fixed="left"
					:selectable="isSelectable"
				/> -->
				<el-table-column align="center" prop="userName" label="人员" width="80" fixed="left" />
				<el-table-column align="center" prop="businessType" label="业务分类" fixed="left" width="130" />
				<el-table-column align="center" prop="workType" label="任务类型" fixed="left" width="100" />
				<el-table-column label="常规工作时间段">
					<el-table-column align="center" label="常规工作时间段" width="1000">
						<template #header>
							<div style="width: 1080px">
								<div @click="flipOutOverTime" :class="isFlipOverTime ? 'hollow-triangle-left' : 'fold-left'"></div>
								<div @click="flipInOverTime" :class="isFlipOverTime ? 'fold-right' : 'hollow-triangle-right'"></div>
								<div class="timeLabel">
									<div v-for="(item, index) in filteredInitTime" :key="index" class="timeLabelItems">
										<span :style="{ marginLeft: item.time.split(':')[0] === '14' ? -17 + 'px' : -7 + 'px' }">
											{{ item.index % 2 == 0 ? (item.time.split(':')[0] === '14' ? '12/14' : item.time.split(':')[0]) : '' }}</span
										>
										<!-- <span v-show="item == '17:30'" style="margin-left: 50px">18</span> -->
									</div>
								</div>
							</div>
						</template>
						<template #default="scope">
							<TaskTime ref="taskTiemref" :filter-work-type="formInline.work_type" :details-info="scope.row" @refresh="getTableData"></TaskTime>
						</template>
					</el-table-column>
				</el-table-column>
				<el-table-column label="加班工作时间段" v-if="!hideOverTimeColumn" :class-name="isFlipOverTime ? 'flip-over-time' : 'flip-in-time'">
					<el-table-column align="center" label="加班工作时间段" width="680" :class-name="isFlipOverTime ? 'flip-over-time' : 'flip-in-time'">
						<template #header>
							<div style="width: 1080px">
								<div class="timeLabel">
									<div class="timeLabel">
										<div v-for="(item, index) in initOverTime" class="timeLabelItems" :key="index">
											<span style="margin-left: -7px"> {{ index % 2 == 0 ? item.split(':')[0] : '' }}</span>
											<!-- <span v-show="item == '21:30'" style="margin-left: 50px">22</span> -->
										</div>
									</div>
								</div>
							</div>
						</template>
						<template #default="scope">
							<OverTimeTask
								ref="taskTiemref"
								:filter-work-type="formInline.work_type"
								:details-info="scope.row"
								@refresh="getTableData"
							></OverTimeTask>
						</template>
					</el-table-column>
				</el-table-column>
				<!-- <el-table-column v-if="formInline.work_type === '原定安排'" align="center" label="操作" width="150" fixed="right">
					<template #default="scope">
						<el-button type="primary" link @click="handleCopyPrevDay(scope.row)"> 复用前一日工作安排 </el-button>
					</template>
				</el-table-column> -->
			</el-table>
		</div>
		<BatchArrangeDialog ref="batchDialogRef" :selected-users="multipleSelection" @refresh="getTableData" />
	</div>
</template>

<script setup lang="ts" name="TaskList">
import { onMounted, ref, reactive, defineAsyncComponent, computed } from 'vue';
import { getDatesOfCurrentWeek, getCurrentDate, getWeekDates } from './common';
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue';
import { useTaskApi } from '/@/api/task/index';
import { useUserApi } from '/@/api/user/index';
import BatchArrangeDialog from './components/BatchArrangeDialog.vue';

const userApi = useUserApi();
const taskApi = useTaskApi();
const TaskTime = defineAsyncComponent(() => import('./components/newTaskTime.vue'));
const OverTimeTask = defineAsyncComponent(() => import('./components/overtimeTask.vue'));



interface FormInline {
	real_name: string;
	group: string;
	searchDate: string;
	work_type?: string;
	ids: [];
	is_arrange: any;
}

const formInline = reactive<FormInline>({
	real_name: '',
	group: '',
	searchDate: '',
	work_type: '', // 展示内容
	ids: [],
	is_arrange: '', // 任务状态
});

// 添加工作类型枚举
const workTypeEnum = [
	{ label: '全部', value: '' },
	{ label: '原定安排', value: '原定安排' },
	{ label: '实际执行', value: '实际执行' },
];

// 添加任务状态枚举
const arrangeStatusEnum = [
	{ label: '全部', value: '' },
	{ label: '已安排', value: 1 },
	{ label: '未安排', value: 2 },
];

const markValue = [
	{ label: '未填写', color: '#fff' },
	{ label: '原定任务', color: '#ffe9e8' },
	{ label: '实际工作', color: '#ffeead' },
	{ label: '已反馈', color: '#c3ead5' },
];

const tableData = ref([]);
const loading = ref(false);

// 时间切换相关字段及方法
const taskTiemref = ref(); // 时间选择组件
const tabActive = ref(''); // tab激活的日期
const tabActiveweek = ref(''); //具体周几
const weeklyDate = ref(''); // 周日期
const startWeekDate = ref(''); // 记录每周周一的日期

const initTime = computed(() => {
	const arr = [];
	for (let i = 10; i <= 18; i++) {
		let h = i < 10 ? `0${i}` : i;
		if (i != 18) {
			arr.push(`${h}:00`, `${h}:30`);
		} else {
			arr.push(`${h}:00`);
		}
	}
	return arr;
});

// 过滤后的时间数组
const filteredInitTime = computed(() => {
	return initTime.value
		.filter(item => !['12:00', '12:30', '13:00', '13:30'].includes(item))
		.map((time, index) => ({ time, index }));
});

// 初始化加班时间
const initOverTime = computed(() => {
	const arr = [];
	for (let i = 18; i <= 22; i++) {
		let h = i < 10 ? `0${i}` : i;
		if (i != 22) {
			arr.push(`${h}:00`, `${h}:30`);
		} else {
			arr.push(`${h}:00`);
		}
	}
	return arr;
});

// 级联选择器配置
const cascaderProps = {
	multiple: true,
	checkStrictly: false, // 保持原有逻辑，父子关联
	value: 'id',
	label: 'name',
	children: 'users',
	disabled: 'disabled', // 启用禁用功能
	emitPath: false,
	expandTrigger: 'hover',
};

// 级联选择器选项数据
const cascaderOptions = ref<any[]>([]);

// 获取特性组数据
const getCharacteristicGroupData = async () => {
	try {
		const res = await userApi.getCharacteristicGroup({is_leader:0});
		if (res?.code === 200) {
			// 处理数据格式
			const options = res.data.map((group: any) => {
				const filteredUsers = group.users
					.filter((user: any) => !user.is_delete && user.is_test) // 过滤掉已删除的用户和未完成测试的用户
					.map((user: any) => ({
						id: user.id,
						name: user.real_name || user.username
					}));
				
				return {
					id: group.id || group.name,
					name: group.name,
					users: filteredUsers,
					disabled: filteredUsers.length === 0 // 没有子用户时禁用checkbox
				};
			}).sort((a: any, b: any) => {
				// 有子用户的排在前面，没有子用户的排在后面
				if (a.users.length > 0 && b.users.length === 0) return -1;
				if (a.users.length === 0 && b.users.length > 0) return 1;
				return 0; // 保持原有顺序
			});
			cascaderOptions.value = options;
		}
	} catch (error) {
		// 获取特性组数据失败
	}
};

// 获取表格数据
const getTableData = async () => {
	const params: {
		date: string;
		business_type: string;
		real_name: string;
		work_type?: string;
		ids?: any;
		is_arrange?: any;
	} = {
		date: tabActive.value,
		business_type: formInline.group,
		real_name: formInline.real_name,
	};
	// 当选择了具体的工作类型时才添加到查询参数中
	if (formInline.work_type) {
		params.work_type = formInline.work_type;
	}
	if (formInline.ids && formInline.ids.length > 0) {
		// 对ids进行去重处理
		const uniqueIds = Array.from(new Set(formInline.ids));
		params.ids = uniqueIds.join(',');
	}
	if (formInline.is_arrange !== '') {
		params.is_arrange = formInline.is_arrange;
	}
	loading.value = true;
	try {
		const { code, data } = await taskApi.getTaskInfoList(params);
		if (code === 200) {
			loading.value = false;
			const arr: any = [];
			// 处理数据
			data.forEach((item: any) => {
				if (item.task.length && item.real_name && item.business_type) {
					item.task.forEach((subItem: any) => {
						arr.push({
							userName: item.real_name,
							userId: item.id,
							businessType: item.group_name,
							workType: subItem.work_type,
							date: subItem.date,
							weekDay: subItem.week_day,
							work: subItem.work,
							taskId: subItem.id ? subItem.id : null,
						});
					});
				}
			});
			tableData.value = arr;
		} else {
			loading.value = false;
			tableData.value = [];
		}
	} catch (error) {
		loading.value = false;
		tableData.value = [];
	}
};

/** 表头合并控制 */
const spanHeader2 = ({ row, rowIndex, columnIndex }: any) => {
	let comStyle = {};
	// 统一处理表头合并，不再区分工作类型（因为已经屏蔽了checkbox列）
	if (rowIndex === 0) {
		row[2].colSpan = 0; // 将表头第一列和第二列合并，内容展示为第二列的内容
		row[1].colSpan = 2;
		if (columnIndex === 2) {
			// 将表头第一列隐藏
			return {
				display: 'none',
			};
		}
	}
	return comStyle;
};

/** 无多选列 合并行、列 */
const spanMethod2 = ({ row, column, rowIndex, columnIndex }: any) => {
	if (columnIndex === 0 || columnIndex === 1) {
		const currentValue = row[column.property];
		const preRow = tableData.value[rowIndex - 1];
		//上一行这一列的数据
		const preValue = preRow ? preRow[column.property] : null;
		// 如果当前值和上一行的值相同，则将当前单元格隐藏
		if (currentValue === preValue) {
			return { rowspan: 0, colspan: 0 };
		} else {
			let rowspan = 1;
			// 计算应该合并的行数
			for (let i = rowIndex + 1; i < tableData.value.length; i++) {
				const nextRow = tableData.value[i];
				const nextValue = nextRow[column.property];
				if (nextValue === currentValue) {
					rowspan++;
				} else {
					break;
				}
			}
			return { rowspan, colspan: 1 };
		}
	}
};



// 可以添加选择相关的方法
// 添加选中数据存储
const multipleSelection: any = ref([]);
const batchDialogRef = ref();
const handleSelectionChange = (selection: any[]) => {
	multipleSelection.value = selection;
};

// 添加批量安排方法
const handleBatchArrange = () => {
	batchDialogRef.value?.show();
};

// 切换tab
const handleClickDate = (tab: any) => {
	tabActiveweek.value = weeklyDates.value.find((item: any) => item.date === tab).weekly;
	getTableData();
};

// 设置日期的tab栏数据
const weeklyDates: any = ref([
	{ weekly: '周一', date: '', abbr: '' },
	{ weekly: '周二', date: '', abbr: '' },
	{ weekly: '周三', date: '', abbr: '' },
	{ weekly: '周四', date: '', abbr: '' },
	{ weekly: '周五', date: '', abbr: '' },
	{ weekly: '周六', date: '', abbr: '' },
	{ weekly: '周日', date: '', abbr: '' },
]);

const setWeeklyDates = (weekly: any[]) => {
	weekly.forEach((item, index) => {
		weeklyDates.value[index].date = item;
		weeklyDates.value[index].abbr = formatWeeklyDate(item);
	});
	// 记录每个周的周一 用来查上一周和下一周
	startWeekDate.value = weeklyDates.value[0].date;
	// 记录周的开始-结束日期
	weeklyDate.value = weeklyDates.value[0].date + '一' + weeklyDates.value[weeklyDates.value.length - 1].date;
};

const formatWeeklyDate = (dateStr: string) => {
	if (!dateStr) return '';
	// 将字符串转换为日期对象
	let date = new Date(dateStr);
	// 获取月和日，并格式化输出
	let formattedDate = date.getMonth() + 1 + '.' + date.getDate();
	return formattedDate;
};

// 上一周或者下一周
const optionWeekClick = (i: number) => {
	const optionWeekArr = getWeekDates(startWeekDate.value, i);
	setWeeklyDates(optionWeekArr);
	tabActive.value = optionWeekArr[0];
};

// 获取工作组
// 工作组的枚举
const groupEnum: any = ref([]);
const getUserGroupFn = async () => {
	const res = await userApi.getUserGroup();
	const arr: any = [];
	res.data?.forEach((item: any) => {
		arr.push({
			label: item.name,
			value: item.id,
		});
	});
	groupEnum.value = arr;
};

// 添加新的搜索处理函数
const handleSearch = () => {
	getTableData();
};

const handleDateChange = () => {
	if (formInline.searchDate) {
		tabActive.value = formInline.searchDate;
		const weekly = getDatesOfCurrentWeek(tabActive.value);
		setWeeklyDates(weekly);
	} else {
		handleSearch();
	}
};



// 初始化当天内容
const initializeTime = () => {
	formInline.real_name = '';
	formInline.group = '';
	formInline.searchDate = '';
	formInline.work_type = '';
	formInline.ids = [];
	formInline.is_arrange = '';
	tabActive.value = getCurrentDate(); // tab激活时的时间
	// 获取当前周的所有日期
	const weelky = getDatesOfCurrentWeek(tabActive.value);
	setWeeklyDates(weelky);
	getTableData();
};

const isFlipOverTime = ref(false);
const tableRef = ref();
const hideOverTimeColumn = ref(false);

// 折叠加班时间
const flipOutOverTime = () => {
	if (isFlipOverTime.value) return;
	isFlipOverTime.value = true;
	setTimeout(() => {
		hideOverTimeColumn.value = true;
	}, 1000);
};

// 展开加班时间
const flipInOverTime = () => {
	if (!isFlipOverTime.value) return;
	isFlipOverTime.value = false;
	hideOverTimeColumn.value = false;
};

onMounted(() => {
	initializeTime();
	getUserGroupFn();
	getCharacteristicGroupData();
	getTableData();
});
</script>

<style scoped lang="scss">
:deep(.el-table--large .el-table__cell) {
	padding: 0px;
}
.layout-padding {
	// padding: 0px !important;
}
.pd15 {
	padding: 15px;
	padding-bottom: 8px;
}
.system-role-padding {
	.fold-left {
		display: none;
		position: absolute;
		right: 0;
		top: -20px;
		width: 0;
		height: 0;
		border-top: 10px solid transparent; /* 斜边 */
		border-bottom: 10px solid transparent; /* 斜边 */
		border-right: 13px solid #666; /* 底边 */
		cursor: pointer;
	}
	.hollow-triangle-left {
		display: none;
		position: absolute;
		right: 0;
		top: -20px;
		width: 0;
		height: 0;
		border-top: 10px solid transparent;
		border-bottom: 10px solid transparent;
		border-right: 13px solid #666; /* 主要边框形成左三角形 */
		cursor: pointer;

		&::after {
			content: '';
			position: absolute;
			left: 2px; /* 调整位置使内部三角形形成空心效果 */
			top: -9px;
			width: 0;
			height: 0;
			border-top: 9px solid transparent;
			border-bottom: 9px solid transparent;
			border-right: 10px solid #f5f7fa; /* 背景色覆盖形成空心 */
		}
	}

	.fold-right {
		display: none;
		position: absolute;
		right: -15px;
		top: -20px;
		width: 0;
		height: 0;
		border-top: 10px solid transparent; /* 斜边 */
		border-bottom: 10px solid transparent; /* 斜边 */
		border-left: 13px solid #666; /* 底边 */
		cursor: pointer;
	}
	.hollow-triangle-right {
		display: none;
		position: absolute;
		right: -14px; // 从-15px调整为-14px（缩小1px）
		top: -10px;
		transform: translateY(-50%);
		width: 0;
		height: 0;
		border-top: 10px solid transparent; // 高度缩小1px
		border-bottom: 10px solid transparent; // 高度缩小1px
		border-left: 12px solid #666; // 宽度12px（外框）
		cursor: pointer;

		&::after {
			content: '';
			position: absolute;
			left: -11px;
			top: -9px;
			width: 0;
			height: 0;
			border-top: 9px solid transparent;
			border-bottom: 9px solid transparent;
			border-left: 10px solid #f5f7fa;
		}
	}
	:deep(.flip-over-time) {
		animation: flipOutY 1s forwards; /* 1秒内完成翻转并消失 */
		transform-origin: left center;
	}
	@keyframes flipOutY {
		0% {
			transform: rotateY(0);
			opacity: 1;
		}
		50% {
			transform: rotateY(90deg);
			opacity: 0.5;
		}
		100% {
			transform: rotateY(180deg);
			opacity: 0;
			display: none;
		}
	}
	:deep(.flip-in-time) {
		animation: flipInY 1s forwards; /* 1秒内完成翻转并消失 */
		transform-origin: left center;
	}
	@keyframes flipInY {
		0% {
			transform: rotateY(180deg);
			opacity: 0;
			display: none;
		}
		50% {
			transform: rotateY(90deg);
			opacity: 0.5;
		}
		100% {
			transform: rotateY(0deg);
			opacity: 1;
		}
	}
}
.system-user-search {
	.el-form {
		min-height: 40px;
		gap: 10px;
		margin-bottom: 10px;

		:deep(.el-input__wrapper) {
			height: 35px;
		}
		.form-item-h {
			width: 1310px;
			display: flex;
			justify-content: space-between;
			&.space-between {
				justify-content: space-between;
			}
			.left {
				width: 1310px;
				display: flex;
				justify-content: space-between;
			}
			.el-form-item {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-right: 60px;
			}
		}
	}
	:deep(.el-icon--right) {
		margin-left: 10px;
	}
}
.group-btn {
	width: 103px;
	height: 35px;
}
.info {
	width: 1310px;
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	gap: 15px;
	margin: 10px 0;
	justify-content: space-between;
	position: relative;
	.info-date {
		font-weight: 600;
		font-size: 16px;
		cursor: pointer;
	}
}
.mark {
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	gap: 12px;
	position: absolute;
	top: 23px;
	right: 60px;
	margin-bottom: -85px;
	z-index: 1;

	.mark-item {
		display: flex;
		align-items: center;

		.mark-item-icon {
			display: inline-block;
			width: 12px;
			height: 12px;
			border: 1px solid #ccc;
			margin-right: 3px;
		}
	}
}
:deep(.el-table thead.is-group th.el-table__cell) {
	text-align: center;
	color: black;
}
:deep(.el-table .cell) {
	line-height: 14px;
	height: 100%;
}
:deep(.el-table thead) {
	height: 45px;
}
:deep(.el-tabs--card > .el-tabs__header) {
	position: relative;
	box-sizing: content-box;
	&::after {
		content: '';
		display: block;
		width: 100%;
		height: 16px;
		border-left: 1px solid #e4e7ed;
		background-color: #fff;
	}
}
:deep(.el-tabs__nav-wrap) {
	margin-bottom: 0;
}
:deep(.el-tabs--card > .el-tabs__header .el-tabs__item) {
	border-right: 1px solid #f2f2f2;
	border-left: none;
	background-color: #e4e7ed;
	&:nth-last-child(1) {
		border-right: none;
	}
}
:deep(.el-tabs--card > .el-tabs__header .el-tabs__item.is-active) {
	background-color: #fff;
}

.timeLabel {
	width: 100%;
	display: flex;
	height: 16px;
	.timeLabelItems {
		width: 80px;
		height: 16px;
		text-align: left;
	}
}

:deep(.el-table__header) {
	.el-checkbox {
		height: 40px;
		display: flex;
		align-items: center;
		justify-content: center;
	}
}

:deep(.el-table__body) {
	.el-checkbox {
		height: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
	}
}
</style>
