export const tableData = [
    {
		userName: '张三',              // 人员姓名
        userId:66,  // 人员id
		businessType: '生态测试业务',  // 业务类型
		workType: '原定工作',   // 工作类型
        leader:'leader1', // 工作反馈人
        date:'2024-10-14 周一', // 工作时间
        // 工作具体详情
		work:[
            { startTime: '12:00', endTime: '14:00', status: 3, leader: 'leader2', details: '小游戏测试1', workType: '原定工作' },
            { startTime: '10:00', endTime: '11:00', status: 2, leader: 'leader', details: '小游戏测试1', workType: '原定工作' },
            { startTime: '16:00', endTime: '16:30', status: 2, leader: 'leader2', details: '小游戏测试1222', workType: '原定工作' },       
        ]
	},
    {
		userName: '张三',              // 人员姓名
        userId:66,  // 人员id
		businessType: '生态测试业务',  // 业务类型
		workType: '实际工作',   // 工作类型 
        leader:'leader1', // 工作反馈人
        date:'2024-10-14 周一', // 工作时间
        // 工作具体详情
		work:[
            { startTime: '12:00', endTime: '14:00', status: 3, leader: 'leader2', details: '小游戏测试1', workType: '实际工作' },
            { startTime: '9:00', endTime: '9:30', status: 2, leader: 'leader', details: '小游戏测试1', workType: '实际工作' },
            { startTime: '15:00', endTime: '17:30', status: 2, leader: 'leader2', details: '小游戏测试1222', workType: '实际工作' },           
        ]
	},
    {
		userName: '王五',              // 人员姓名
        userId:66,  // 人员id
		businessType: 'WXUP业务',  // 业务类型
		workType: '原定工作',   // 工作类型
        leader:'leader1', // 工作反馈人
        date:'2024-10-14 周一', // 工作时间
        // 工作具体详情
		work:[
            { startTime: '12:00', endTime: '14:00', status: 3, leader: 'leader2', details: '小游戏测试1', workType: '原定工作' },
            { startTime: '10:00', endTime: '11:00', status: 2, leader: 'leader', details: '小游戏测试1', workType: '原定工作' },
            { startTime: '16:00', endTime: '16:30', status: 2, leader: 'leader2', details: '小游戏测试1222', workType: '原定工作' },   
        ]
	},
    {
		userName: '王五',              // 人员姓名
        userId:66,  // 人员id
		businessType: 'WXUP业务',  // 业务类型
		workType: '实际工作',   // 工作类型 
        leader:'leader1', // 工作反馈人
        date:'2024-10-14 周一', // 工作时间
        // 工作具体详情
		work:[
            { startTime: '12:00', endTime: '14:00', status: 3, leader: 'leader2', details: '小游戏测试1', workType: '实际工作' },
            { startTime: '9:00', endTime: '11:00', status: 2, leader: 'leader', details: '小游戏测试1', workType: '实际工作' },
            { startTime: '19:00', endTime: '22:00', status: 2, leader: 'leader2', details: '小游戏测试1222', workType: '实际工作' },           
        ]
	},
    {
		userName: '老六',              // 人员姓名
        userId:66,  // 人员id
		businessType: 'WXUP业务',  // 业务类型
		workType: '原定工作',   // 工作类型 
        leader:'leader1', // 工作反馈人
        date:'2024-10-14 周一', // 工作时间
        // 工作具体详情
		work:[
            { startTime: '12:00', endTime: '14:00', status: 3, leader: 'leader2', details: '小游戏测试1', workType: '原定工作' },
            { startTime: '9:00', endTime: '9:30', status: 2, leader: 'leader', details: '小游戏测试1', workType: '原定工作' },
            { startTime: '15:00', endTime: '17:30', status: 2, leader: 'leader2', details: '小游戏测试1222', workType: '原定工作' },           
        ]
	},
    {
		userName: '老六',              // 人员姓名
        userId:66,  // 人员id
		businessType: 'WXUP业务',  // 业务类型
		workType: '实际工作',   // 工作类型 
        leader:'leader1', // 工作反馈人
        date:'2024-10-14 周一', // 工作时间
        // 工作具体详情
		work:[
            { startTime: '12:00', endTime: '14:00', status: 3, leader: 'leader2', details: '小游戏测试1', workType: '实际工作' },
            { startTime: '10:00', endTime: '11:00', status: 2, leader: 'leader', details: '小游戏测试1', workType: '实际工作' },
            { startTime: '16:00', endTime: '16:30', status: 2, leader: 'leader2', details: 'WXUP业务小游戏测试1222', workType: '实际工作' },   
        ]
	}
]