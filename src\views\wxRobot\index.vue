<template>
	<div class="system-role-container layout-padding">
		<div class="system-role-padding layout-padding-auto layout-padding-view">
			<div class="system-user-search mb15">
				<!-- <div>
					<el-input
						v-model="state.tableData.param.real_name"
						size="default"
						placeholder="请输入用户姓名"
						style="max-width: 180px"
						clearable
						@clear="handleClear"
					>
					</el-input>
					<el-button size="default" type="primary" class="ml10" @click="getTableData">
						<el-icon>
							<ele-Search />
						</el-icon>
						查询
					</el-button>
					<el-button size="default" type="success" class="ml10" @click="onOpenAddRole('add')">
						<el-icon>
							<ele-FolderAdd />
						</el-icon>
						新增企微机器人
					</el-button>
				</div> -->
				<div>
					<el-button size="default" type="primary" class="ml10" @click="onOpenMessageDailog('add')">
						<el-icon>
							<ele-FolderAdd />
						</el-icon>
						添加自定义消息推送内容
					</el-button>
					<el-button size="default" type="success" class="ml10" @click="onOpenRobotDailog">
						<el-icon>
							<ele-FolderAdd />
						</el-icon>
						企微机器人配置
					</el-button>
				</div>
			</div>
			<el-table :data="state.tableData.data" v-loading="state.tableData.loading" style="width: 100%">
				<el-table-column type="index" label="序号" width="60" />
				<el-table-column prop="inform" label="消息标题" show-overflow-tooltip></el-table-column>
				<el-table-column prop="start_time" label="执行时间" show-overflow-tooltip>
					<template #default="scope">
						{{ scope.row.date == '每周' ? '每' + scope.row.week + scope.row.start_time : scope.row.date + scope.row.start_time }}
					</template>
				</el-table-column>
				<el-table-column prop="webhook_name" label="推送机器人" show-overflow-tooltip></el-table-column>
				<el-table-column prop="type" label="消息类型" show-overflow-tooltip>
					<template #default="scope">
						{{ scope.row.type === 1 ? '特殊通知' : '自定义通知' }}
					</template>
				</el-table-column>
				<el-table-column prop="is_start" label="启用状态" show-overflow-tooltip>
					<template #default="scope">
						<el-switch v-model="scope.row.is_start" style="--el-switch-on-color: #13ce66" @change="statusChange($event, scope.row)" />
					</template>
				</el-table-column>
				<el-table-column prop="comment" label="消息内容" show-overflow-tooltip></el-table-column>
				<el-table-column label="操作" width="150">
					<template #default="scope">
						<el-button @click="onOpenMessageDailog('edit', scope.row)" size="small" text type="primary">编辑</el-button>
						<el-button @click="onRowDel(scope.row)" size="small" text type="primary">删除</el-button>
					</template>
				</el-table-column>
			</el-table>
			<el-pagination
				@size-change="onHandleSizeChange"
				@current-change="onHandleCurrentChange"
				class="mt15"
				:pager-count="5"
				:page-sizes="[10, 20, 30]"
				v-model:current-page="state.tableData.param.page"
				background
				v-model:page-size="state.tableData.param.page_size"
				layout="total, sizes, prev, pager, next, jumper"
				:total="state.tableData.total"
			>
			</el-pagination>
		</div>
		<RobotDialog ref="robotDialogRef"></RobotDialog>
		<MessageDialog ref="messageDialogRef" @refresh="getTableData"></MessageDialog>
	</div>
</template>

<script setup lang="ts" name="WxRobot">
import { defineAsyncComponent, reactive, onMounted, ref } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { useRobotApi } from '/@/api/robot/index';
const robotApi = useRobotApi();
// 引入组件
const RobotDialog = defineAsyncComponent(() => import('./robotDialog.vue'));
const MessageDialog = defineAsyncComponent(() => import('./messageDialog.vue'));

// 定义变量内容
const robotDialogRef = ref();
const messageDialogRef = ref();
const state = reactive<any>({
	tableData: {
		data: [],
		total: 0,
		loading: false,
		param: {
			page: 1,
			page_size: 10,
		},
	},
});
// 初始化表格数据
const getTableData = async () => {
	state.tableData.loading = true;
	try {
		const { code, data, count } = await robotApi.getMessageList(state.tableData.param);
		if (code === 200) {
			state.tableData.data = data;
			state.tableData.total = count;
			state.tableData.loading = false;
		} else {
			ElMessage.error('请求接口错误,请稍后再试!');
			state.tableData.loading = false;
		}
	} catch {
		state.tableData.data = [];
		state.tableData.loading = false;
	}
};

const onOpenRobotDailog = () => {
	robotDialogRef.value.openDialog();
};
// 改变状态
const statusChange = async (val: any, row: any) => {
    const params ={
        is_start:val,
        id:row.id
    }
	await robotApi.editWxMessagePatch(params);
	ElMessage.success(val?'启用成功':'关闭成功');
	getTableData();
};
// 删除内容
const onRowDel = (row: any) => {
	ElMessageBox.confirm(`确认要删除该推送内容吗?`, '提示', {
		confirmButtonText: '确认',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await robotApi.delWxMessage(row.id);
			ElMessage.success('删除成功');
			getTableData();
		})
		.catch(() => {});
};
// 分页改变
const onHandleSizeChange = (val: number) => {
	state.tableData.param.page_size = val;
	getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
	state.tableData.param.page = val;
	getTableData();
};
//打开消息配置弹窗
const onOpenMessageDailog = (type: any, row: any = {}) => {
	messageDialogRef.value.openDialog(type, row);
};
// 页面加载时
onMounted(() => {
	getTableData();
});
// 测试数据
</script>

<style scoped lang="scss">
.system-role-container {
	.system-role-padding {
		padding: 15px;
		.el-table {
			flex: 1;
		}
	}
	.system-user-search {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}
}
</style>
