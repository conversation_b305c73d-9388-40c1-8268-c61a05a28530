<template>
	<div class="system-role-dialog-container">
		<el-dialog :title="state.dialog.title" v-model="state.dialog.isShowDialog" width="769px" @close="closeDialog">
			<el-form ref="messageDialogFormRef" :model="state.ruleForm" :rules="rules" size="default" label-position="left">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="消息标题" prop="inform">
							<el-input v-model="state.ruleForm.inform" placeholder="请输入通知内容" clearable></el-input>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="企微机器人" prop="webhook">
							<el-select v-model="state.ruleForm.webhook" placeholder="请选择机器人" clearable class="w100">
								<el-option v-for="item in robotList" :key="item.value" :label="item.label" :value="item.value">{{ item.label }}</el-option>
							</el-select>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="10" class="mb20">
					<el-col :span="8">
						<el-form-item :label="'执行时间'" prop="date">
							<el-select style="width: 100%; margin-right: 10px" v-model="state.ruleForm.date" placeholder="请选择配置类型" @change="changedate">
								<el-option value="每日" label="每日"> </el-option>
								<el-option value="每周" label="每周"> </el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="8" v-if="state.ruleForm.date === '每周'">
						<el-form-item label="" prop="week">
							<el-select v-if="state.ruleForm.date === '每周'" style="width: 100%" v-model="state.ruleForm.week" placeholder="请选择每周执行日期">
								<el-option v-for="item in weekDay" :value="item.value" :label="item.label" :key="item.value"> </el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="8" v-if="state.ruleForm.date !== 'custom'">
						<el-form-item label="" prop="start_time">
							<el-time-picker
								style="width: 100%"
								v-model="state.ruleForm.start_time"
								value-format="HH:mm"
								placeholder="选择具体时间"
								:clearable="false"
							/>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="35">
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20 ml10">
						<el-form-item :label="'消息内容'" prop="comment">
							<el-input v-model="state.ruleForm.comment" :autosize="{ minRows: 3, maxRows: 6 }" type="textarea" placeholder="请输入备注内容" />
						</el-form-item>
					</el-col>
				</el-row>
				<el-row :gutter="35">
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20 ml10">
						<el-form-item :label="'启用状态'" prop="is_start">
							<el-switch v-model="state.ruleForm.is_start" style="--el-switch-on-color: #13ce66" />
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="onCancel" size="default">取 消</el-button>
					<el-button type="primary" @click="onSubmit(messageDialogFormRef)" size="default">{{ state.dialog.submitTxt }}</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts" name="robotMessageDialog">
import { reactive, ref } from 'vue';
import { useRobotApi } from '/@/api/robot/index';
import { ElMessage } from 'element-plus';
const robotApi = useRobotApi();
// 定义子组件向父组件传值/事件
const emit = defineEmits(['refresh']);
const messageDialogFormRef = ref();
// 定义变量内容
const state: any = reactive({
	ruleForm: {
		inform: '',
		webhook: '',
		comment: '',
		date: '',
		start_time: '',
		// dayOfMonth: '',
		week: null,
		is_start: false,
		type: 2,
	},
	dialog: {
		isShowDialog: false,
		type: '',
		title: '消息配置',
		submitTxt: '确 定',
	},
});
const robotList = ref([]);
const rules = reactive<any>({
	inform: [{ required: true, message: '必填项', trigger: 'blur' }],
	webhook: [{ required: true, message: '必填项', trigger: 'blur' }],
	date: [{ required: true, message: '必填项', trigger: 'change' }],
	start_time: [{ required: true, message: '必填项', trigger: 'blur' }],
	week: [{ required: true, message: '必填项', trigger: 'change' }],
	dayOfMonth: [{ required: true, message: '必填项', trigger: 'change' }],
});
// 打开弹窗
const openDialog = (type: string, row: any) => {
	if (type === 'edit') {
		const obj = JSON.parse(JSON.stringify(row));
		for (let key in state.ruleForm) {
			state.ruleForm[key] = obj[key];
		}
		state.ruleForm.id = obj.id;
	}
	state.dialog.type = type;
	state.dialog.isShowDialog = true;
	getRobotList();
};
// 关闭弹窗
const closeDialog = () => {
	state.dialog.isShowDialog = false;
	clearForm();
	messageDialogFormRef.value.resetFields();
};
// 编辑弹框类型改变时
const changedate = () => {
	state.ruleForm.start_time = '';
	state.ruleForm.week = null;
	// state.ruleForm.dayOfMonth = ''
};
// 星期几
const weekDay = [
	{ value: '周一', label: '周一' },
	{ value: '周二', label: '周二' },
	{ value: '周三', label: '周三' },
	{ value: '周四', label: '周四' },
	{ value: '周五', label: '周五' },
	{ value: '周六', label: '周六' },
	{ value: '周日', label: '周日' },
];
const clearForm = () => {};

// 取消
const onCancel = () => {
	closeDialog();
};
// 获取机器人列表
const getRobotList = async () => {
	try {
		const { code, data } = await robotApi.getWebhookInfo({ page: 1, page_size: 999 });
		if (code === 200) {
			const arr: any = [];
			data.forEach((item: any) => {
				arr.push({
					label: item.name,
					value: item.id,
				});
			});
			robotList.value = arr;
		} else {
			ElMessage.error('请求机器人列表接口失败,请稍后再试!');
			robotList.value = [];
		}
	} catch {
		robotList.value = [];
	}
};
// 提交
const onSubmit = async (formEl: any) => {
	if (!formEl) return;
	await formEl.validate(async (valid: any) => {
		if (valid) {
			const params = JSON.parse(JSON.stringify(state.ruleForm));
			if (params.id) {
				//编辑
				await robotApi.editWxMessage(params);
				ElMessage.success('编辑成功!');
			} else {
				//新增
				await robotApi.addWxMessage(params);
				ElMessage.success('新增成功!');
			}
			closeDialog();
			emit('refresh');
		}
	});
};

// 暴露变量
defineExpose({
	openDialog,
});
</script>

<style scoped lang="scss">
.system-role-dialog-container {
	.menu-data-tree {
		width: 100%;
		border: 1px solid var(--el-border-color);
		border-radius: var(--el-input-border-radius, var(--el-border-radius-base));
		padding: 5px;
	}
	.ml10 {
		margin-left: 10px;
	}
}
</style>
