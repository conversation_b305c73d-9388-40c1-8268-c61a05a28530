<template>
	<div class="system-role-dialog-container">
		<el-dialog :title="'企微机器人'" v-model="state.dialog.isShowDialog" width="50%" @close="closeDialog">
			<el-button size="small" text type="success" @click="creatRobot">创建企微机器人</el-button>
			<el-form ref="robotDialogFormRef" :model="state.tableData" size="default">
				<el-table
					:data="state.tableData.data"
					v-loading="state.tableData.loading"
					style="width: 100%"
					:max-height="400"
					border
					:header-cell-style="tableHeaderColor"
				>
					<el-table-column type="index" label="序号" width="60" />
					<el-table-column prop="name" label="机器人名称" show-overflow-tooltip>
						<template #default="scope">
							<div v-if="scope.row.isEdit">
								<el-form-item :prop="`data.${scope.$index}.name`" :rules="[{ required: true, message: '请填写机器人名称', trigger: 'blur' }]">
									<el-input v-model.trim="scope.row.name"></el-input>
								</el-form-item>
							</div>
							<div v-else>
								{{ scope.row.name }}
							</div>
						</template>
					</el-table-column>
					<el-table-column prop="url" label="机器人webhook" show-overflow-tooltip>
						<template #default="scope">
							<div v-if="scope.row.isEdit">
								<el-form-item :prop="`data.${scope.$index}.url`" :rules="[{ required: true, message: '请填写机器人webhook', trigger: 'blur' }]">
									<el-input v-model.trim="scope.row.url"></el-input>
								</el-form-item>
							</div>
							<div v-else>
								{{ scope.row.url }}
							</div>
						</template>
					</el-table-column>
					<el-table-column prop="descriptions" label="描述" show-overflow-tooltip>
						<template #default="scope">
							<div v-if="scope.row.isEdit">
								<el-form-item :prop="`data.${scope.$index}.descriptions`">
									<el-input v-model.trim="scope.row.descriptions"></el-input>
								</el-form-item>
							</div>
							<div v-else>
								{{ scope.row.descriptions }}
							</div>
						</template>
					</el-table-column>
					<el-table-column label="操作" width="150">
						<template #default="scope">
							<div v-if="scope.row.isEdit">
								<el-button size="small" text type="primary" @click="onSubmit(scope.row)">保存</el-button>
								<el-button size="small" text type="primary" @click="cancelClick(scope.row, scope.$index)">取消</el-button>
							</div>
							<div v-else>
								<el-button size="small" text type="primary" @click="editClick(scope.row)">编辑</el-button>
								<el-button size="small" text type="primary" @click="onRowDel(scope.row)">删除</el-button>
							</div>
						</template>
					</el-table-column>
				</el-table>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="onCancel" size="default">关 闭</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>
<script setup lang="ts" name="systemRoleDialog">
import { reactive, ref } from 'vue';
import { useRobotApi } from '/@/api/robot/index';
import { ElMessage, ElMessageBox } from 'element-plus';
const robotApi = useRobotApi();
const robotDialogFormRef = ref();
// 定义变量内容
const state = reactive<any>({
	tableData: {
		data: [],
		total: 0,
		loading: false,
		param: {
			page: 1,
			page_size: 999,
		},
	},
	dialog: {
		isShowDialog: false,
	},
});
const oldData: any = ref([]);
// 表格头部样式
const tableHeaderColor = () => {
	return {
		background: '#d4d7de',
		color: 'black',
	};
};
const getTableData = async () => {
	try {
		const { code, data } = await robotApi.getWebhookInfo(state.tableData.param);
		if (code === 200) {
			state.tableData.data = state.tableData.data = JSON.parse(JSON.stringify(data));
			oldData.value = JSON.parse(JSON.stringify(data));
			state.tableData.loading = false;
		} else {
			ElMessage.error('请求接口错误,请稍后再试!');
			state.tableData.loading = false;
		}
	} catch {
		state.tableData.data = [];
		oldData.value = [];
		state.tableData.loading = false;
	}
};
// 打开弹窗
const openDialog = () => {
	getTableData();
	state.dialog.isShowDialog = true;
};
// 关闭弹窗
const closeDialog = () => {
	state.dialog.isShowDialog = false;
	// robotDialogFormRef.value.resetFields();
};
// 创建机器人
const creatRobot = () => {
	state.tableData.data.unshift({ name: '', url: '', descriptions: '', isEdit: true });
	oldData.value.unshift({ name: '', url: '', descriptions: '', isEdit: true }); // 老数据同步
};
// 取消
const onCancel = () => {
	closeDialog();
};
const editClick = (row: any) => {
	row.isEdit = true;
};
// 取消编辑或新增
const cancelClick = (row: any, index: any) => {
	// 编辑状态取消
	if (row.id) {
		state.tableData.data[index] = JSON.parse(JSON.stringify(oldData.value[index]));
	} else {
		//新增状态取消
		state.tableData.data.splice(index, 1);
		oldData.value.splice(index, 1);
	}
};
// 删除机器人
const onRowDel = (row: any) => {
	ElMessageBox.confirm(`确认要删除该机器人吗?`, '提示', {
		confirmButtonText: '确认',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await robotApi.delWebhookInfo(row.id);
			ElMessage.success('删除成功');
			getTableData();
		})
		.catch(() => {});
};
// 提交
const onSubmit = async (row: any) => {
	robotDialogFormRef.value.validate(async (valid: any) => {
		if (valid) {
			const param: any = JSON.parse(JSON.stringify(row));
			delete param.isEdit;
			if (param.id) {
				//编辑
				await robotApi.editWebhookInfo(param);
				ElMessage.success('编辑成功!');
			} else {
				await robotApi.addWebhook(param);
				ElMessage.success('添加成功!');
			}
			getTableData();
		} else {
			ElMessage.error('请完善表单内容!');
		}
	});
};
// 暴露变量
defineExpose({
	openDialog,
});
</script>

<style scoped lang="scss">
.system-role-dialog-container {
	.menu-data-tree {
		width: 100%;
		border: 1px solid var(--el-border-color);
		border-radius: var(--el-input-border-radius, var(--el-border-radius-base));
		padding: 5px;
	}
}
</style>
