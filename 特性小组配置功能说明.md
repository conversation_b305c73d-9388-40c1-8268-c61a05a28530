# 特性小组配置功能说明

## 功能概述
在用户管理页面添加了"特性小组配置"功能，用户点击该按钮后会弹出一个表格弹窗，展示特性小组的列表内容，并支持对特性小组进行增删改查操作。

## 主要功能

### 1. 特性小组列表展示
- 显示小组名称
- 显示小组管理员（从 `group_admin_user` 数组中匹配 `characteristic_group_admin` 字段）
- 显示成员数量
- 支持表格排序和筛选

### 2. 新增特性小组
- 点击"新增特性小组"按钮
- 填写小组名称
- 可选择小组管理员

### 3. 设置管理员
- 点击"设置管理员"按钮
- 小组名称为只读状态
- 可从当前小组成员中选择管理员
- 管理员选项来自该小组的 `users` 字段

### 4. 移除管理员
- 点击"移除管理员"按钮
- 显示当前小组的所有管理员
- 支持多选要移除的管理员
- 如果小组没有管理员，按钮会被禁用

## 技术实现

### 1. 新增API接口
在 `src/api/user/index.ts` 中添加了以下接口：
- `getCharacteristicGroupList()` - 获取特性小组列表
- `addCharacteristicGroup()` - 添加特性小组
- `editCharacteristicGroup()` - 编辑特性小组
- `deleteCharacteristicGroup()` - 删除特性小组
- `setCharacteristicGroupAdmin()` - 设置特性小组管理员
- `removeCharacteristicGroupAdmin()` - 移除特性小组管理员

### 2. 新增弹窗组件
创建了 `src/views/system/user/characteristicGroupConfigDialog.vue` 组件：
- 主弹窗：展示特性小组列表
- 设置管理员弹窗：新增/编辑特性小组表单
- 移除管理员弹窗：多选移除管理员

### 3. 数据结构处理
正确处理了后端返回的数据结构：

**管理员信息显示：**
```javascript
// 管理员信息从 group_admin_user 数组中获取
const getGroupAdminNames = (group) => {
  if (!group.group_admin_user || !group.id) return [];
  
  const admins = group.group_admin_user.filter(user => 
    user.characteristic_group_admin === group.id
  );
  
  return admins.map(admin => admin.real_name || admin.username).filter(Boolean);
};
```

**管理员选项获取：**
```javascript
// 在打开编辑弹窗时，直接从当前小组的users字段获取可选管理员
const onOpenEditDialog = (row) => {
  // 设置当前小组的用户列表作为管理员选项
  state.currentGroupUsers = row.users?.filter(user => !user.is_delete) || [];
  // ... 其他逻辑
};
```

**移除管理员处理：**
```javascript
// 获取当前小组的管理员并支持多选移除
const onOpenRemoveAdminDialog = (row) => {
  const admins = row.group_admin_user.filter(user => 
    user.characteristic_group_admin === row.id
  );
  state.removeAdminDialog.adminList = admins;
};
```

### 4. 主页面集成
在 `src/views/system/user/index.vue` 中：
- 引入了新的弹窗组件
- 添加了点击事件处理函数
- 绑定了刷新数据的回调

## 使用方式

1. 进入用户管理页面
2. 点击"特性小组配置"按钮
3. 在弹出的表格中查看所有特性小组
4. 可以进行以下操作：
   - 新增特性小组：点击"新增特性小组"按钮
   - 设置管理员：点击表格中的"设置管理员"按钮
   - 移除管理员：点击表格中的"移除管理员"按钮
   - 查看成员数量和管理员信息

## 注意事项

1. 管理员信息是从 `group_admin_user` 数组中动态获取的
2. 一个特性小组可以有多个管理员
3. 设置管理员时，小组名称为只读状态
4. 管理员选项只显示当前小组的成员（从 `users` 字段获取）
5. 移除管理员支持多选，通过 `ids` 参数传递多个ID（逗号分隔）
6. 自动过滤已删除的用户（`is_delete: false`）
7. 所有操作都会自动刷新父组件的数据
